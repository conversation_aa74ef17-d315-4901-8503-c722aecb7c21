#!/bin/bash

# 更新 session-threat-detector 模块中的包名
# 从 com.geeksec.threatdetector 改为 com.geeksec.sessionthreatdetector

echo "开始更新 session-threat-detector 模块的包名..."

# 更新 Java 源文件中的包声明
find flink-jobs/session-threat-detector/src -name "*.java" -type f -exec sed -i '' 's/package com\.geeksec\.threatdetector/package com.geeksec.sessionthreatdetector/g' {} \;

# 更新 Java 源文件中的 import 语句
find flink-jobs/session-threat-detector/src -name "*.java" -type f -exec sed -i '' 's/import com\.geeksec\.threatdetector/import com.geeksec.sessionthreatdetector/g' {} \;

# 更新其他模块中对 threatdetector 包的引用
find flink-jobs -name "*.java" -type f -not -path "*/session-threat-detector/*" -exec sed -i '' 's/import com\.geeksec\.threatdetector/import com.geeksec.sessionthreatdetector/g' {} \;

echo "Java 包名更新完成"

# 更新 pom.xml 文件
echo "更新 Maven 配置..."
sed -i '' 's/<artifactId>threat-detector<\/artifactId>/<artifactId>session-threat-detector<\/artifactId>/g' flink-jobs/session-threat-detector/pom.xml
sed -i '' 's/<name>threat-detector<\/name>/<name>session-threat-detector<\/name>/g' flink-jobs/session-threat-detector/pom.xml

# 更新父 pom.xml 中的模块引用
sed -i '' 's/<module>threat-detector<\/module>/<module>session-threat-detector<\/module>/g' flink-jobs/pom.xml

echo "Maven 配置更新完成"

# 更新 Dockerfile
echo "更新 Dockerfile..."
sed -i '' 's/threat-detector/session-threat-detector/g' flink-jobs/session-threat-detector/Dockerfile

echo "Dockerfile 更新完成"

echo "包名更新脚本执行完成！"
