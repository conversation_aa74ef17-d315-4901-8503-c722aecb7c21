{{- if .Values.infrastructure.flink.enabled -}}
{{- if and .Values.infrastructure.flink.operator.enabled -}}
{{- $flinkOperatorDep := (index (dict "name" "flink-kubernetes-operator") "name") -}}
{{- range .Chart.Dependencies -}}
  {{- if eq .Name $flinkOperatorDep -}}
    {{- $flinkOperatorVersion := .Version -}}
    {{- $minVersion := "1.11.0" -}}
    {{- if semverCompare "< $minVersion" $flinkOperatorVersion -}}
      {{- fail (printf "Flink Kubernetes Operator version %s is too old. Minimum required version is %s" $flinkOperatorVersion $minVersion) -}}
    {{- end -}}
  {{- end -}}
{{- end -}}
{{- end -}}

{{- if .Values.features.graph.enabled -}}
{{- $deploymentConfig := dict "name" "flink-graph-builder" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.graph-builder.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.graph-builder.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.graph-builder.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.graph-builder.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.graph-builder.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.session-threat-detector.enabled -}}
---
{{- $deploymentConfig := dict "name" "flink-session-threat-detector" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.session-threat-detector.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.session-threat-detector.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.session-threat-detector.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.session-threat-detector.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.session-threat-detector.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}

{{/* session-processor 已更名为 data-warehouse-processor */}}

{{- if .Values.infrastructure.flink.jobs.certificate-analyzer.enabled -}}
---
{{- $deploymentConfig := dict "name" "flink-certificate-analyzer" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.certificate-analyzer.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.certificate-analyzer.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.certificate-analyzer.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.certificate-analyzer.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.certificate-analyzer.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.data-warehouse-processor.enabled -}}
---
{{- $deploymentConfig := dict "name" "flink-data-warehouse-processor" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.data-warehouse-processor.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.data-warehouse-processor.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.data-warehouse-processor.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.data-warehouse-processor.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.data-warehouse-processor.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}

{{- end -}}
