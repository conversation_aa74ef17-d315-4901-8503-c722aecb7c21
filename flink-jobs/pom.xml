<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>nta-platform</artifactId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>flink-jobs</artifactId>
    <packaging>pom</packaging>
    <name>flink-jobs</name>
    <description>Flink Jobs for Network Traffic Analysis</description>

    <properties>
        <!-- Plugin Versions -->
        
        <!-- Flink Related Versions -->
        <flink.version>1.20.1</flink.version>
        <flink.connector.kafka.version>3.3.0-1.20</flink.connector.kafka.version>
        <flink.connector.jdbc.version>3.3.0-1.20</flink.connector.jdbc.version>
        <flink.connector.elasticsearch7.version>3.1.0-1.20</flink.connector.elasticsearch7.version>
        <flink.connector.doris.version>25.1.0</flink.connector.doris.version>
        <nebula-flink-connector.version>3.8.0</nebula-flink-connector.version>

        <!-- 其他Flink相关依赖版本 -->
        <hadoop.aws.version>3.4.1</hadoop.aws.version>
        <jackson.version>2.18.2</jackson.version>
        <jackson.datatype.jsr310.version>2.18.2</jackson.datatype.jsr310.version>
        <logback.version>1.5.12</logback.version>
        <certificate.transparency.version>0.1.0</certificate.transparency.version>
        <jchardet.version>1.0</jchardet.version>
        <chill.thrift.version>0.10.0</chill.thrift.version>
        <thrift.version>0.21.0</thrift.version>
        <httpclient.version>4.5.14</httpclient.version>
        <opennlp.version>2.5.0</opennlp.version>
        <public.suffix.list.version>2.2.0</public.suffix.list.version>
        <geoip2.version>4.2.1</geoip2.version>

        <!-- 测试相关 -->
        <hamcrest.version>1.3</hamcrest.version>

        <!-- 构建插件版本 -->
        <maven.shade.plugin.version>3.5.0</maven.shade.plugin.version>
        
        <!-- Lombok -->
        <lombok.version>1.18.36</lombok.version>
        
        <!-- Custom Dependencies -->
        <slf4j.version>2.0.16</slf4j.version>
        <logstash.logback.encoder.version>8.0</logstash.logback.encoder.version>
        <commons.lang3.version>3.17.0</commons.lang3.version>
        <commons.io.version>2.18.0</commons.io.version>
        <guava.version>33.3.1-jre</guava.version>
        <protobuf.version>4.29.2</protobuf.version>
        <kafka.clients.version>3.9.0</kafka.clients.version>
        <commons.pool.version>1.6</commons.pool.version>
        <mysql.connector.version>8.0.40</mysql.connector.version>
        <fastjson.version>1.2.83</fastjson.version>
        <jedis.version>5.2.0</jedis.version>
        <elasticsearch.version>7.17.26</elasticsearch.version>
        <minio.version>8.5.14</minio.version>
        <os.maven.plugin.version>1.7.1</os.maven.plugin.version>
        <ua.parse.verion>7.28.0</ua.parse.verion>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Common Dependencies -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.logback.encoder.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.clients.version}</version>
            </dependency>
            <!-- Flink Core Dependencies (provided scope as they are included in Flink distribution) -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-core</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-runtime</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-clients</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-api-java-bridge</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-runtime</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-statebackend-rocksdb</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Flink Connectors -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-kafka</artifactId>
                <version>${flink.connector.kafka.version}</version>
            </dependency>

            <!-- Flink JSON Format -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-json</artifactId>
                <version>${flink.version}</version>
            </dependency>
            
            <!-- Flink Files Connector -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-files</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <!-- Nebula Flink Connector -->
            <dependency>
                <groupId>com.vesoft</groupId>
                <artifactId>nebula-flink-connector</artifactId>
                <version>${nebula-flink-connector.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-jdbc</artifactId>
                <version>${flink.connector.jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-elasticsearch7</artifactId>
                <version>${flink.connector.elasticsearch7.version}</version>
            </dependency>

            <!-- Doris Flink Connector -->
            <dependency>
                <groupId>org.apache.doris</groupId>
                <artifactId>flink-doris-connector-1.20</artifactId>
                <version>${flink.connector.doris.version}</version>
            </dependency>

            <!-- GeoIP2 -->
            <dependency>
                <groupId>com.maxmind.geoip2</groupId>
                <artifactId>geoip2</artifactId>
                <version>${geoip2.version}</version>
            </dependency>

            <!-- Flink Test Dependencies -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-test-utils</artifactId>
                <version>${flink.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-runtime</artifactId>
                <version>${flink.version}</version>
                <type>test-jar</type>
                <classifier>tests</classifier>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
                <classifier>tests</classifier>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>

            <!-- S3 文件系统依赖 -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-s3-fs-hadoop</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <!-- Hadoop AWS 依赖，用于S3兼容存储 -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-aws</artifactId>
                <version>${hadoop.aws.version}</version>
            </dependency>

            <!-- JSON处理相关 -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.datatype.jsr310.version}</version>
            </dependency>


            <!-- 证书解析相关 -->
            <dependency>
                <groupId>org.certificate-transparency</groupId>
                <artifactId>ctlog</artifactId>
                <version>${certificate.transparency.version}</version>
            </dependency>

            <!-- 字符集检测 -->
            <dependency>
                <groupId>net.sourceforge.jchardet</groupId>
                <artifactId>jchardet</artifactId>
                <version>${jchardet.version}</version>
            </dependency>

            <!-- Apache Thrift相关 -->
            <dependency>
                <groupId>com.twitter</groupId>
                <artifactId>chill-thrift</artifactId>
                <version>${chill.thrift.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.esotericsoftware.kryo</groupId>
                        <artifactId>kryo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>${thrift.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- HTTP客户端 -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <!-- NLP工具 -->
            <dependency>
                <groupId>org.apache.opennlp</groupId>
                <artifactId>opennlp-tools</artifactId>
                <version>${opennlp.version}</version>
            </dependency>

            <!-- Public Suffix List -->
            <dependency>
                <groupId>de.malkusch.whois-server-list</groupId>
                <artifactId>public-suffix-list</artifactId>
                <version>${public.suffix.list.version}</version>
            </dependency>
            
            <!-- Commons Pool -->
            <dependency>
                <groupId>commons-pool</groupId>
                <artifactId>commons-pool</artifactId>
                <version>${commons.pool.version}</version>
            </dependency>
            
            <!-- FastJSON -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            
            <!-- Jedis for Redis -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <!-- Elasticsearch High Level REST Client -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- MinIO Client -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            
            <!-- OS Maven Plugin -->
            <dependency>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>${os.maven.plugin.version}</version>
            </dependency>
            <!-- UA Parse -->
            <dependency>
                <groupId>nl.basjes.parse.useragent</groupId>
                <artifactId>yauaa</artifactId>
                <version>${ua.parse.verion}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <modules>
        <module>common</module>
        <module>graph-builder</module>
        <module>session-threat-detector</module>
        <module>certificate-analyzer</module>
        <module>data-warehouse-processor</module>
        <module>alarm-processor</module>
    </modules>

    <!-- 公共依赖，适用于大多数子模块 -->
    <!-- 依赖管理，子模块按需引入 -->

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${surefire.plugin.version}</version>
                    <configuration>
                        <useSystemClassLoader>false</useSystemClassLoader>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Test*.java</include>
                        </includes>
                    </configuration>
                    <dependencies>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <configuration>
                        <images>
                            <image>
                                <name>
                                    ${docker.registry}/${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                                <build>
                                    <args>
                                        <FLINK_VERSION>${flink.version}</FLINK_VERSION>
                                    </args>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven.jar.plugin.version}</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <classpathPrefix>lib/</classpathPrefix>
                                <mainClass>${main.class}</mainClass>
                                <useUniqueVersions>false</useUniqueVersions>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven.shade.plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <createDependencyReducedPom>false</createDependencyReducedPom>
                                <transformers>
                                    <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                        <mainClass>${main.class}</mainClass>
                                    </transformer>
                                    <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                                </transformers>
                                <filters>
                                    <filter>
                                        <artifact>*:*</artifact>
                                        <excludes>
                                            <exclude>META-INF/*.SF</exclude>
                                            <exclude>META-INF/*.DSA</exclude>
                                            <exclude>META-INF/*.RSA</exclude>
                                        </excludes>
                                    </filter>
                                </filters>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>