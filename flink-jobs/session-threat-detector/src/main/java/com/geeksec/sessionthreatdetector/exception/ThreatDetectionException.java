package com.geeksec.sessionthreatdetector.exception;

/**
 * 威胁检测异常基类
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class ThreatDetectionException extends Exception {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public ThreatDetectionException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public ThreatDetectionException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 异常原因
     */
    public ThreatDetectionException(Throwable cause) {
        super(cause);
    }
}
