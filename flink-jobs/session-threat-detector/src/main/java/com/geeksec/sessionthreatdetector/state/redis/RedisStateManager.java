package com.geeksec.sessionthreatdetector.state.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigurationManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.params.SetParams;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import static com.geeksec.common.core.constants.ConfigConstants.*;

/**
 * Redis状态管理器
 * 为威胁检测系统提供统一的Redis状态管理功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class RedisStateManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    
    /**
     * Redis连接池
     */
    private static volatile JedisPool jedisPool = null;
    
    /**
     * Redis主机地址
     */
    private static final String REDIS_HOST_ADDR = CONFIG.get(REDIS_HOST, "localhost");
    
    /**
     * Redis端口
     */
    private static final int REDIS_PORT_NUM = CONFIG.getInt(REDIS_PORT, 6379);
    
    /**
     * Redis密码
     */
    private static final String REDIS_PWD = CONFIG.get(REDIS_PASSWORD, "");
    
    /**
     * Redis数据库索引
     */
    private static final int REDIS_DB_INDEX = CONFIG.getInt("redis.database", 0);
    
    /**
     * Redis超时时间（毫秒）
     */
    private static final int REDIS_TIMEOUT_MS = CONFIG.getInt(REDIS_TIMEOUT, 10000);
    
    /**
     * Redis键过期时间（秒）
     */
    private static final int REDIS_EXPIRE_SECOND = CONFIG.getInt(REDIS_KEY_TTL, 3600 * 24 * 7);
    
    /**
     * 键前缀
     */
    public static final String KEY_PREFIX_THREAT_DETECTOR = "threat_detector:";
    public static final String KEY_PREFIX_ATTACK_CHAIN = "attack_chain:";
    public static final String KEY_PREFIX_ALARM_STATE = "alarm_state:";
    public static final String KEY_PREFIX_DETECTOR_STATE = "detector_state:";
    public static final String KEY_PREFIX_SESSION_STATE = "session_state:";
    public static final String KEY_PREFIX_STATISTICS = "statistics:";
    public static final String KEY_PREFIX_CACHE = "cache:";
    public static final String KEY_PREFIX_LOCK = "lock:";
    
    /**
     * 统计信息
     */
    private final AtomicLong operationCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final Map<String, AtomicLong> operationStats = new ConcurrentHashMap<>();
    
    /**
     * 单例实例
     */
    private static volatile RedisStateManager instance = null;
    
    /**
     * 私有构造函数
     */
    private RedisStateManager() {
        initializeRedisPool();
    }
    
    /**
     * 获取单例实例
     * 
     * @return RedisStateManager实例
     */
    public static RedisStateManager getInstance() {
        if (instance == null) {
            synchronized (RedisStateManager.class) {
                if (instance == null) {
                    instance = new RedisStateManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化Redis连接池
     */
    private void initializeRedisPool() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            return;
        }
        
        try {
            JedisPoolConfig config = new JedisPoolConfig();
            config.setMaxTotal(200);
            config.setMaxIdle(50);
            config.setMinIdle(10);
            config.setMaxWaitMillis(10000);
            config.setTestOnBorrow(true);
            config.setTestOnReturn(true);
            config.setTestWhileIdle(true);
            config.setTimeBetweenEvictionRunsMillis(30000);
            config.setMinEvictableIdleTimeMillis(60000);
            
            if (StringUtils.isNotEmpty(REDIS_PWD)) {
                jedisPool = new JedisPool(config, REDIS_HOST_ADDR, REDIS_PORT_NUM, REDIS_TIMEOUT_MS, REDIS_PWD, REDIS_DB_INDEX);
            } else {
                jedisPool = new JedisPool(config, REDIS_HOST_ADDR, REDIS_PORT_NUM, REDIS_TIMEOUT_MS, null, REDIS_DB_INDEX);
            }
            
            // 测试连接
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.ping();
                log.info("Redis连接池初始化成功: {}:{}/{}", REDIS_HOST_ADDR, REDIS_PORT_NUM, REDIS_DB_INDEX);
            }
            
        } catch (Exception e) {
            log.error("Redis连接池初始化失败: {}:{}", REDIS_HOST_ADDR, REDIS_PORT_NUM, e);
            throw new RuntimeException("Redis连接池初始化失败", e);
        }
    }
    
    /**
     * 获取Jedis实例
     * 
     * @return Jedis实例
     */
    private Jedis getJedis() {
        if (jedisPool == null || jedisPool.isClosed()) {
            initializeRedisPool();
        }
        
        try {
            return jedisPool.getResource();
        } catch (Exception e) {
            log.error("获取Jedis实例失败", e);
            errorCount.incrementAndGet();
            throw new RuntimeException("获取Redis连接失败", e);
        }
    }
    
    /**
     * 执行Redis操作
     * 
     * @param operation 操作名称
     * @param action Redis操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    private <T> T executeRedisOperation(String operation, RedisOperation<T> action) {
        operationCount.incrementAndGet();
        operationStats.computeIfAbsent(operation, k -> new AtomicLong(0)).incrementAndGet();
        
        try (Jedis jedis = getJedis()) {
            return action.execute(jedis);
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("Redis操作失败: {}", operation, e);
            throw new RuntimeException("Redis操作失败: " + operation, e);
        }
    }
    
    /**
     * 设置字符串值
     * 
     * @param key 键
     * @param value 值
     * @param expireSeconds 过期时间（秒）
     * @return 操作结果
     */
    public String setString(String key, String value, int expireSeconds) {
        return executeRedisOperation("setString", jedis -> {
            if (expireSeconds > 0) {
                return jedis.setex(key, expireSeconds, value);
            } else {
                return jedis.set(key, value);
            }
        });
    }
    
    /**
     * 设置字符串值（使用默认过期时间）
     * 
     * @param key 键
     * @param value 值
     * @return 操作结果
     */
    public String setString(String key, String value) {
        return setString(key, value, REDIS_EXPIRE_SECOND);
    }
    
    /**
     * 获取字符串值
     * 
     * @param key 键
     * @return 值
     */
    public String getString(String key) {
        return executeRedisOperation("getString", jedis -> jedis.get(key));
    }
    
    /**
     * 设置对象值
     * 
     * @param key 键
     * @param object 对象
     * @param expireSeconds 过期时间（秒）
     * @return 操作结果
     */
    public String setObject(String key, Object object, int expireSeconds) {
        String jsonValue = JSON.toJSONString(object);
        return setString(key, jsonValue, expireSeconds);
    }
    
    /**
     * 设置对象值（使用默认过期时间）
     * 
     * @param key 键
     * @param object 对象
     * @return 操作结果
     */
    public String setObject(String key, Object object) {
        return setObject(key, object, REDIS_EXPIRE_SECOND);
    }
    
    /**
     * 获取对象值
     * 
     * @param key 键
     * @param clazz 对象类型
     * @param <T> 对象类型
     * @return 对象
     */
    public <T> T getObject(String key, Class<T> clazz) {
        return executeRedisOperation("getObject", jedis -> {
            String jsonValue = jedis.get(key);
            if (StringUtils.isNotEmpty(jsonValue)) {
                return JSON.parseObject(jsonValue, clazz);
            }
            return null;
        });
    }
    
    /**
     * 检查键是否存在
     * 
     * @param key 键
     * @return 是否存在
     */
    public boolean exists(String key) {
        return executeRedisOperation("exists", jedis -> jedis.exists(key));
    }
    
    /**
     * 删除键
     * 
     * @param key 键
     * @return 删除的键数量
     */
    public Long delete(String key) {
        return executeRedisOperation("delete", jedis -> jedis.del(key));
    }
    
    /**
     * 批量删除键
     * 
     * @param keys 键数组
     * @return 删除的键数量
     */
    public Long delete(String... keys) {
        return executeRedisOperation("batchDelete", jedis -> jedis.del(keys));
    }
    
    /**
     * 设置键的过期时间
     * 
     * @param key 键
     * @param seconds 过期时间（秒）
     * @return 操作结果
     */
    public Long expire(String key, int seconds) {
        return executeRedisOperation("expire", jedis -> jedis.expire(key, seconds));
    }
    
    /**
     * 获取键的剩余生存时间
     * 
     * @param key 键
     * @return 剩余生存时间（秒）
     */
    public Long ttl(String key) {
        return executeRedisOperation("ttl", jedis -> jedis.ttl(key));
    }
    
    /**
     * 原子递增
     * 
     * @param key 键
     * @return 递增后的值
     */
    public Long incr(String key) {
        return executeRedisOperation("incr", jedis -> jedis.incr(key));
    }
    
    /**
     * 原子递增指定值
     * 
     * @param key 键
     * @param increment 递增值
     * @return 递增后的值
     */
    public Long incrBy(String key, long increment) {
        return executeRedisOperation("incrBy", jedis -> jedis.incrBy(key, increment));
    }
    
    /**
     * 原子递减
     * 
     * @param key 键
     * @return 递减后的值
     */
    public Long decr(String key) {
        return executeRedisOperation("decr", jedis -> jedis.decr(key));
    }
    
    /**
     * 设置哈希字段值
     * 
     * @param key 键
     * @param field 字段
     * @param value 值
     * @return 操作结果
     */
    public Long hset(String key, String field, String value) {
        return executeRedisOperation("hset", jedis -> jedis.hset(key, field, value));
    }
    
    /**
     * 获取哈希字段值
     * 
     * @param key 键
     * @param field 字段
     * @return 值
     */
    public String hget(String key, String field) {
        return executeRedisOperation("hget", jedis -> jedis.hget(key, field));
    }
    
    /**
     * 获取哈希所有字段和值
     * 
     * @param key 键
     * @return 字段和值的映射
     */
    public Map<String, String> hgetAll(String key) {
        return executeRedisOperation("hgetAll", jedis -> jedis.hgetAll(key));
    }
    
    /**
     * 删除哈希字段
     * 
     * @param key 键
     * @param fields 字段数组
     * @return 删除的字段数量
     */
    public Long hdel(String key, String... fields) {
        return executeRedisOperation("hdel", jedis -> jedis.hdel(key, fields));
    }
    
    /**
     * 检查哈希字段是否存在
     * 
     * @param key 键
     * @param field 字段
     * @return 是否存在
     */
    public Boolean hexists(String key, String field) {
        return executeRedisOperation("hexists", jedis -> jedis.hexists(key, field));
    }
    
    /**
     * 添加到集合
     * 
     * @param key 键
     * @param members 成员数组
     * @return 添加的成员数量
     */
    public Long sadd(String key, String... members) {
        return executeRedisOperation("sadd", jedis -> jedis.sadd(key, members));
    }
    
    /**
     * 获取集合所有成员
     * 
     * @param key 键
     * @return 成员集合
     */
    public Set<String> smembers(String key) {
        return executeRedisOperation("smembers", jedis -> jedis.smembers(key));
    }
    
    /**
     * 检查是否为集合成员
     * 
     * @param key 键
     * @param member 成员
     * @return 是否为成员
     */
    public Boolean sismember(String key, String member) {
        return executeRedisOperation("sismember", jedis -> jedis.sismember(key, member));
    }
    
    /**
     * 从集合中移除成员
     * 
     * @param key 键
     * @param members 成员数组
     * @return 移除的成员数量
     */
    public Long srem(String key, String... members) {
        return executeRedisOperation("srem", jedis -> jedis.srem(key, members));
    }
    
    /**
     * 获取集合大小
     * 
     * @param key 键
     * @return 集合大小
     */
    public Long scard(String key) {
        return executeRedisOperation("scard", jedis -> jedis.scard(key));
    }
    
    /**
     * 分布式锁 - 尝试获取锁
     * 
     * @param lockKey 锁键
     * @param lockValue 锁值
     * @param expireSeconds 过期时间（秒）
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String lockValue, int expireSeconds) {
        return executeRedisOperation("tryLock", jedis -> {
            SetParams params = new SetParams().nx().ex(expireSeconds);
            String result = jedis.set(lockKey, lockValue, params);
            return "OK".equals(result);
        });
    }
    
    /**
     * 分布式锁 - 释放锁
     * 
     * @param lockKey 锁键
     * @param lockValue 锁值
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String lockValue) {
        return executeRedisOperation("releaseLock", jedis -> {
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Object result = jedis.eval(script, Collections.singletonList(lockKey), Collections.singletonList(lockValue));
            return Long.valueOf(1).equals(result);
        });
    }
    
    /**
     * 获取匹配模式的所有键
     * 
     * @param pattern 模式
     * @return 键集合
     */
    public Set<String> keys(String pattern) {
        return executeRedisOperation("keys", jedis -> jedis.keys(pattern));
    }
    
    /**
     * 清理过期数据
     * 
     * @param keyPattern 键模式
     * @param batchSize 批次大小
     * @return 清理的键数量
     */
    public long cleanupExpiredData(String keyPattern, int batchSize) {
        return executeRedisOperation("cleanupExpiredData", jedis -> {
            Set<String> keys = jedis.keys(keyPattern);
            if (keys.isEmpty()) {
                return 0L;
            }
            
            long deletedCount = 0;
            List<String> batch = new ArrayList<>();
            
            for (String key : keys) {
                batch.add(key);
                if (batch.size() >= batchSize) {
                    deletedCount += jedis.del(batch.toArray(new String[0]));
                    batch.clear();
                }
            }
            
            if (!batch.isEmpty()) {
                deletedCount += jedis.del(batch.toArray(new String[0]));
            }
            
            return deletedCount;
        });
    }
    
    /**
     * 获取Redis信息
     * 
     * @return Redis信息
     */
    public String info() {
        return executeRedisOperation("info", Jedis::info);
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    public RedisStatistics getStatistics() {
        return new RedisStatistics(
                operationCount.get(),
                errorCount.get(),
                new HashMap<>(operationStats.entrySet().stream()
                        .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue().get()), HashMap::putAll)),
                jedisPool != null ? jedisPool.getNumActive() : 0,
                jedisPool != null ? jedisPool.getNumIdle() : 0
        );
    }
    
    /**
     * 关闭Redis连接池
     */
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            jedisPool = null;
            log.info("Redis连接池已关闭");
        }
    }
    
    /**
     * Redis操作接口
     * 
     * @param <T> 返回类型
     */
    @FunctionalInterface
    private interface RedisOperation<T> {
        T execute(Jedis jedis) throws Exception;
    }
    
    /**
     * Redis统计信息
     */
    @lombok.Getter
    @lombok.AllArgsConstructor
    public static class RedisStatistics {
        private final long totalOperations;
        private final long errorCount;
        private final Map<String, Long> operationStats;
        private final int activeConnections;
        private final int idleConnections;
        
        public double getErrorRate() {
            return totalOperations > 0 ? (double) errorCount / totalOperations : 0.0;
        }
    }
}
