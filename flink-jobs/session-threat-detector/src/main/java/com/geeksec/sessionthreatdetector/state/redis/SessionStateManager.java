package com.geeksec.sessionthreatdetector.state.redis;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会话状态管理器
 * 负责网络会话状态的Redis存储和管理，支持会话跟踪、异常检测等功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class SessionStateManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Redis状态管理器
     */
    private final RedisStateManager redisStateManager;
    
    /**
     * 会话状态过期时间（秒）- 30分钟
     */
    private static final int SESSION_STATE_TTL = 30 * 60;
    
    /**
     * 会话统计过期时间（秒）- 24小时
     */
    private static final int SESSION_STATS_TTL = 24 * 3600;
    
    /**
     * 活跃会话过期时间（秒）- 10分钟
     */
    private static final int ACTIVE_SESSION_TTL = 10 * 60;
    
    /**
     * 构造函数
     */
    public SessionStateManager() {
        this.redisStateManager = RedisStateManager.getInstance();
    }
    
    /**
     * 创建或更新会话状态
     * 
     * @param sessionId 会话ID
     * @param sessionInfo 会话信息
     */
    public void updateSessionState(String sessionId, SessionInfo sessionInfo) {
        if (sessionId == null || sessionInfo == null) {
            return;
        }
        
        try {
            String sessionKey = buildSessionKey(sessionId);
            sessionInfo.setLastUpdateTime(LocalDateTime.now());
            
            // 保存会话状态
            redisStateManager.setObject(sessionKey, sessionInfo, SESSION_STATE_TTL);
            
            // 添加到活跃会话集合
            String activeSessionsKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "active_sessions";
            redisStateManager.sadd(activeSessionsKey, sessionId);
            redisStateManager.expire(activeSessionsKey, ACTIVE_SESSION_TTL);
            
            // 更新会话索引
            updateSessionIndexes(sessionId, sessionInfo);
            
            // 更新会话统计
            updateSessionStatistics(sessionInfo);
            
            log.debug("会话状态已更新: {}", sessionId);
            
        } catch (Exception e) {
            log.error("更新会话状态失败: {}", sessionId, e);
        }
    }
    
    /**
     * 获取会话状态
     * 
     * @param sessionId 会话ID
     * @return 会话信息
     */
    public SessionInfo getSessionState(String sessionId) {
        if (sessionId == null) {
            return null;
        }
        
        try {
            String sessionKey = buildSessionKey(sessionId);
            return redisStateManager.getObject(sessionKey, SessionInfo.class);
        } catch (Exception e) {
            log.error("获取会话状态失败: {}", sessionId, e);
            return null;
        }
    }
    
    /**
     * 获取活跃会话列表
     * 
     * @return 活跃会话ID列表
     */
    public List<String> getActiveSessions() {
        try {
            String activeSessionsKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "active_sessions";
            Set<String> sessionIds = redisStateManager.smembers(activeSessionsKey);
            return new ArrayList<>(sessionIds);
        } catch (Exception e) {
            log.error("获取活跃会话列表失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据源IP查询会话
     * 
     * @param srcIp 源IP
     * @param limit 限制数量
     * @return 会话ID列表
     */
    public List<String> getSessionsBySrcIp(String srcIp, int limit) {
        if (srcIp == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:src_ip:" + srcIp;
            Set<String> sessionIds = redisStateManager.smembers(indexKey);
            
            return sessionIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("根据源IP查询会话失败: {}", srcIp, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据目标IP查询会话
     * 
     * @param dstIp 目标IP
     * @param limit 限制数量
     * @return 会话ID列表
     */
    public List<String> getSessionsByDstIp(String dstIp, int limit) {
        if (dstIp == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:dst_ip:" + dstIp;
            Set<String> sessionIds = redisStateManager.smembers(indexKey);
            
            return sessionIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("根据目标IP查询会话失败: {}", dstIp, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据协议查询会话
     * 
     * @param protocol 协议
     * @param limit 限制数量
     * @return 会话ID列表
     */
    public List<String> getSessionsByProtocol(String protocol, int limit) {
        if (protocol == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:protocol:" + protocol;
            Set<String> sessionIds = redisStateManager.smembers(indexKey);
            
            return sessionIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("根据协议查询会话失败: {}", protocol, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 查询异常会话
     * 
     * @param limit 限制数量
     * @return 异常会话ID列表
     */
    public List<String> getAnomalousSessions(int limit) {
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:anomalous";
            Set<String> sessionIds = redisStateManager.smembers(indexKey);
            
            return sessionIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("查询异常会话失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 标记会话为异常
     * 
     * @param sessionId 会话ID
     * @param anomalyType 异常类型
     * @param description 异常描述
     */
    public void markSessionAsAnomalous(String sessionId, String anomalyType, String description) {
        if (sessionId == null) {
            return;
        }
        
        try {
            // 获取现有会话信息
            SessionInfo sessionInfo = getSessionState(sessionId);
            if (sessionInfo == null) {
                return;
            }
            
            // 添加异常信息
            SessionAnomaly anomaly = SessionAnomaly.builder()
                    .anomalyType(anomalyType)
                    .description(description)
                    .detectedTime(LocalDateTime.now())
                    .build();
            
            if (sessionInfo.getAnomalies() == null) {
                sessionInfo.setAnomalies(new ArrayList<>());
            }
            sessionInfo.getAnomalies().add(anomaly);
            sessionInfo.setAnomalous(true);
            
            // 更新会话状态
            updateSessionState(sessionId, sessionInfo);
            
            // 添加到异常会话索引
            String anomalousIndexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:anomalous";
            redisStateManager.sadd(anomalousIndexKey, sessionId);
            redisStateManager.expire(anomalousIndexKey, SESSION_STATE_TTL);
            
            log.info("会话已标记为异常: {} -> {}", sessionId, anomalyType);
            
        } catch (Exception e) {
            log.error("标记会话异常失败: {}", sessionId, e);
        }
    }
    
    /**
     * 结束会话
     * 
     * @param sessionId 会话ID
     * @param endReason 结束原因
     */
    public void endSession(String sessionId, String endReason) {
        if (sessionId == null) {
            return;
        }
        
        try {
            SessionInfo sessionInfo = getSessionState(sessionId);
            if (sessionInfo == null) {
                return;
            }
            
            sessionInfo.setEndTime(LocalDateTime.now());
            sessionInfo.setEndReason(endReason);
            sessionInfo.setActive(false);
            
            // 计算会话持续时间
            if (sessionInfo.getStartTime() != null) {
                long durationSeconds = java.time.Duration.between(
                        sessionInfo.getStartTime(), sessionInfo.getEndTime()).getSeconds();
                sessionInfo.setDurationSeconds(durationSeconds);
            }
            
            // 更新会话状态
            updateSessionState(sessionId, sessionInfo);
            
            // 从活跃会话集合中移除
            String activeSessionsKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "active_sessions";
            redisStateManager.srem(activeSessionsKey, sessionId);
            
            log.debug("会话已结束: {} -> {}", sessionId, endReason);
            
        } catch (Exception e) {
            log.error("结束会话失败: {}", sessionId, e);
        }
    }
    
    /**
     * 获取会话统计信息
     * 
     * @param timeRange 时间范围（小时）
     * @return 统计信息
     */
    public SessionStatistics getSessionStatistics(int timeRange) {
        try {
            // 获取活跃会话数量
            List<String> activeSessions = getActiveSessions();
            int activeSessionCount = activeSessions.size();
            
            // 获取异常会话数量
            List<String> anomalousSessions = getAnomalousSessions(1000);
            int anomalousSessionCount = anomalousSessions.size();
            
            // 统计协议分布
            Map<String, Integer> protocolStats = new HashMap<>();
            String protocolStatsKey = RedisStateManager.KEY_PREFIX_STATISTICS + "session:protocol_stats";
            Map<String, String> protocolData = redisStateManager.hgetAll(protocolStatsKey);
            for (Map.Entry<String, String> entry : protocolData.entrySet()) {
                protocolStats.put(entry.getKey(), Integer.parseInt(entry.getValue()));
            }
            
            // 统计异常类型分布
            Map<String, Integer> anomalyTypeStats = new HashMap<>();
            String anomalyStatsKey = RedisStateManager.KEY_PREFIX_STATISTICS + "session:anomaly_stats";
            Map<String, String> anomalyData = redisStateManager.hgetAll(anomalyStatsKey);
            for (Map.Entry<String, String> entry : anomalyData.entrySet()) {
                anomalyTypeStats.put(entry.getKey(), Integer.parseInt(entry.getValue()));
            }
            
            return SessionStatistics.builder()
                    .activeSessionCount(activeSessionCount)
                    .anomalousSessionCount(anomalousSessionCount)
                    .protocolDistribution(protocolStats)
                    .anomalyTypeDistribution(anomalyTypeStats)
                    .statisticsTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取会话统计信息失败", e);
            return SessionStatistics.builder()
                    .activeSessionCount(0)
                    .anomalousSessionCount(0)
                    .protocolDistribution(new HashMap<>())
                    .anomalyTypeDistribution(new HashMap<>())
                    .statisticsTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 清理过期会话
     * 
     * @param maxAgeHours 最大年龄（小时）
     * @return 清理的会话数量
     */
    public long cleanupExpiredSessions(int maxAgeHours) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(maxAgeHours);
            String pattern = RedisStateManager.KEY_PREFIX_SESSION_STATE + "session:*";
            Set<String> sessionKeys = redisStateManager.keys(pattern);
            
            long deletedCount = 0;
            for (String sessionKey : sessionKeys) {
                SessionInfo sessionInfo = redisStateManager.getObject(sessionKey, SessionInfo.class);
                if (sessionInfo != null && sessionInfo.getLastUpdateTime() != null && 
                    sessionInfo.getLastUpdateTime().isBefore(cutoffTime)) {
                    String sessionId = sessionKey.substring(sessionKey.lastIndexOf(":") + 1);
                    deleteSession(sessionId);
                    deletedCount++;
                }
            }
            
            log.info("清理过期会话完成: 清理数量={}, 截止时间={}", deletedCount, cutoffTime);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("清理过期会话失败", e);
            return 0;
        }
    }
    
    /**
     * 删除会话
     * 
     * @param sessionId 会话ID
     */
    public void deleteSession(String sessionId) {
        if (sessionId == null) {
            return;
        }
        
        try {
            // 删除会话基本信息
            String sessionKey = buildSessionKey(sessionId);
            redisStateManager.delete(sessionKey);
            
            // 从索引中移除
            removeFromIndexes(sessionId);
            
            // 从活跃会话集合中移除
            String activeSessionsKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "active_sessions";
            redisStateManager.srem(activeSessionsKey, sessionId);
            
            log.debug("会话已删除: {}", sessionId);
            
        } catch (Exception e) {
            log.error("删除会话失败: {}", sessionId, e);
        }
    }
    
    /**
     * 更新会话索引
     * 
     * @param sessionId 会话ID
     * @param sessionInfo 会话信息
     */
    private void updateSessionIndexes(String sessionId, SessionInfo sessionInfo) {
        // 按源IP索引
        if (sessionInfo.getSrcIp() != null) {
            String srcIpIndexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:src_ip:" + sessionInfo.getSrcIp();
            redisStateManager.sadd(srcIpIndexKey, sessionId);
            redisStateManager.expire(srcIpIndexKey, SESSION_STATE_TTL);
        }
        
        // 按目标IP索引
        if (sessionInfo.getDstIp() != null) {
            String dstIpIndexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:dst_ip:" + sessionInfo.getDstIp();
            redisStateManager.sadd(dstIpIndexKey, sessionId);
            redisStateManager.expire(dstIpIndexKey, SESSION_STATE_TTL);
        }
        
        // 按协议索引
        if (sessionInfo.getProtocol() != null) {
            String protocolIndexKey = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:protocol:" + sessionInfo.getProtocol();
            redisStateManager.sadd(protocolIndexKey, sessionId);
            redisStateManager.expire(protocolIndexKey, SESSION_STATE_TTL);
        }
    }
    
    /**
     * 更新会话统计
     * 
     * @param sessionInfo 会话信息
     */
    private void updateSessionStatistics(SessionInfo sessionInfo) {
        try {
            // 协议统计
            if (sessionInfo.getProtocol() != null) {
                String protocolStatsKey = RedisStateManager.KEY_PREFIX_STATISTICS + "session:protocol_stats";
                redisStateManager.hset(protocolStatsKey, sessionInfo.getProtocol(), 
                        String.valueOf(redisStateManager.hget(protocolStatsKey, sessionInfo.getProtocol()) != null ? 
                                Integer.parseInt(redisStateManager.hget(protocolStatsKey, sessionInfo.getProtocol())) + 1 : 1));
                redisStateManager.expire(protocolStatsKey, SESSION_STATS_TTL);
            }
            
            // 异常统计
            if (sessionInfo.isAnomalous() && sessionInfo.getAnomalies() != null) {
                String anomalyStatsKey = RedisStateManager.KEY_PREFIX_STATISTICS + "session:anomaly_stats";
                for (SessionAnomaly anomaly : sessionInfo.getAnomalies()) {
                    if (anomaly.getAnomalyType() != null) {
                        redisStateManager.hset(anomalyStatsKey, anomaly.getAnomalyType(),
                                String.valueOf(redisStateManager.hget(anomalyStatsKey, anomaly.getAnomalyType()) != null ?
                                        Integer.parseInt(redisStateManager.hget(anomalyStatsKey, anomaly.getAnomalyType())) + 1 : 1));
                    }
                }
                redisStateManager.expire(anomalyStatsKey, SESSION_STATS_TTL);
            }
            
        } catch (Exception e) {
            log.error("更新会话统计失败", e);
        }
    }
    
    /**
     * 从索引中移除会话
     * 
     * @param sessionId 会话ID
     */
    private void removeFromIndexes(String sessionId) {
        String indexPattern = RedisStateManager.KEY_PREFIX_SESSION_STATE + "index:*";
        Set<String> indexKeys = redisStateManager.keys(indexPattern);
        
        for (String indexKey : indexKeys) {
            redisStateManager.srem(indexKey, sessionId);
        }
    }
    
    /**
     * 构建会话键
     * 
     * @param sessionId 会话ID
     * @return 会话键
     */
    private String buildSessionKey(String sessionId) {
        return RedisStateManager.KEY_PREFIX_SESSION_STATE + "session:" + sessionId;
    }
    
    /**
     * 会话信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String sessionId;
        private String srcIp;
        private String dstIp;
        private Integer srcPort;
        private Integer dstPort;
        private String protocol;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private LocalDateTime lastUpdateTime;
        private String endReason;
        private boolean active;
        private boolean anomalous;
        private long durationSeconds;
        private long bytesTransferred;
        private long packetsTransferred;
        private List<SessionAnomaly> anomalies;
        private Map<String, Object> extensions;
    }
    
    /**
     * 会话异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionAnomaly implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String anomalyType;
        private String description;
        private LocalDateTime detectedTime;
        private double severity;
        private Map<String, Object> details;
    }
    
    /**
     * 会话统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private int activeSessionCount;
        private int anomalousSessionCount;
        private Map<String, Integer> protocolDistribution;
        private Map<String, Integer> anomalyTypeDistribution;
        private LocalDateTime statisticsTime;
    }
}
