package com.geeksec.sessionthreatdetector.subscription.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 订阅配置类
 * 统一管理告警订阅相关的配置参数
 * 
 * <AUTHOR>
 */
@Data
@Slf4j
public class SubscriptionConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // ==================== 基础配置 ====================
    
    /**
     * 是否启用订阅功能
     */
    private boolean enabled = true;
    
    /**
     * 是否透传原始告警
     */
    private boolean passthroughAlarms = true;
    
    /**
     * 订阅处理并行度
     */
    private int parallelism = 2;
    
    /**
     * 最大订阅数量
     */
    private int maxSubscriptions = 10000;
    
    /**
     * 最大用户数量
     */
    private int maxUsers = 1000;
    
    // ==================== 匹配配置 ====================
    
    /**
     * 订阅匹配超时时间（毫秒）
     */
    private long matchTimeoutMs = 5000L;
    
    /**
     * 是否启用订阅缓存
     */
    private boolean subscriptionCacheEnabled = true;
    
    /**
     * 订阅缓存过期时间（毫秒）
     */
    private long subscriptionCacheExpirationMs = 300000L; // 5分钟
    
    /**
     * 订阅缓存最大大小
     */
    private int subscriptionCacheMaxSize = 5000;
    
    // ==================== 通知配置 ====================
    
    /**
     * 是否启用异步通知
     */
    private boolean asyncNotificationEnabled = true;
    
    /**
     * 通知队列大小
     */
    private int notificationQueueSize = 10000;
    
    /**
     * 通知发送超时时间（毫秒）
     */
    private long notificationTimeoutMs = 30000L;
    
    /**
     * 通知重试次数
     */
    private int notificationRetryCount = 3;
    
    /**
     * 通知重试间隔（毫秒）
     */
    private long notificationRetryIntervalMs = 5000L;
    
    // ==================== 频率控制配置 ====================
    
    /**
     * 是否启用频率控制
     */
    private boolean frequencyControlEnabled = true;
    
    /**
     * 默认通知间隔（分钟）
     */
    private int defaultNotificationIntervalMinutes = 5;
    
    /**
     * 默认每日最大通知次数
     */
    private int defaultMaxNotificationsPerDay = 100;
    
    /**
     * 批量通知阈值
     */
    private int defaultBatchThreshold = 10;
    
    /**
     * 批量等待时间（分钟）
     */
    private int defaultBatchWaitMinutes = 5;
    
    // ==================== 渠道配置 ====================
    
    /**
     * 邮件配置
     */
    private EmailConfig emailConfig;
    
    /**
     * 短信配置
     */
    private SmsConfig smsConfig;
    
    /**
     * 钉钉配置
     */
    private DingTalkConfig dingTalkConfig;
    
    /**
     * 企业微信配置
     */
    private WeChatWorkConfig weChatWorkConfig;
    
    /**
     * Webhook配置
     */
    private WebhookConfig webhookConfig;
    
    // ==================== 模板配置 ====================
    
    /**
     * 是否启用自定义模板
     */
    private boolean customTemplateEnabled = true;
    
    /**
     * 模板缓存过期时间（毫秒）
     */
    private long templateCacheExpirationMs = 600000L; // 10分钟
    
    /**
     * 模板最大大小（字符数）
     */
    private int templateMaxSize = 10000;
    
    // ==================== 监控配置 ====================
    
    /**
     * 是否启用统计信息
     */
    private boolean statisticsEnabled = true;
    
    /**
     * 统计信息输出间隔（秒）
     */
    private int statisticsIntervalSeconds = 60;
    
    /**
     * 配置刷新间隔（秒）
     */
    private int configRefreshIntervalSeconds = 300;
    
    /**
     * 是否启用详细日志
     */
    private boolean verboseLoggingEnabled = false;
    
    /**
     * 慢处理阈值（毫秒）
     */
    private long slowProcessingThresholdMs = 1000L;
    
    // ==================== 存储配置 ====================
    
    /**
     * 订阅数据存储类型
     */
    private String subscriptionStorageType = "memory"; // memory, redis, database
    
    /**
     * 存储连接配置
     */
    private Map<String, Object> storageConfig;
    
    /**
     * 是否启用订阅持久化
     */
    private boolean subscriptionPersistenceEnabled = false;
    
    /**
     * 持久化间隔（秒）
     */
    private int persistenceIntervalSeconds = 300;
    
    // ==================== 安全配置 ====================
    
    /**
     * 是否启用订阅权限控制
     */
    private boolean subscriptionAuthEnabled = true;
    
    /**
     * 允许的用户角色列表
     */
    private List<String> allowedUserRoles;
    
    /**
     * 是否启用通知内容过滤
     */
    private boolean notificationContentFilterEnabled = true;
    
    /**
     * 敏感信息过滤规则
     */
    private List<String> sensitiveInfoFilterRules;
    
    /**
     * 邮件配置
     */
    @Data
    public static class EmailConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String smtpHost;
        private int smtpPort = 587;
        private String username;
        private String password;
        private boolean tlsEnabled = true;
        private String fromAddress;
        private String fromName = "威胁检测系统";
        private int connectionTimeoutMs = 10000;
        private int readTimeoutMs = 10000;
    }
    
    /**
     * 短信配置
     */
    @Data
    public static class SmsConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String provider; // aliyun, tencent, etc.
        private String accessKeyId;
        private String accessKeySecret;
        private String signName;
        private String templateCode;
        private String endpoint;
        private int timeoutMs = 5000;
    }
    
    /**
     * 钉钉配置
     */
    @Data
    public static class DingTalkConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String webhookUrl;
        private String secret;
        private String accessToken;
        private int timeoutMs = 5000;
        private boolean atAllEnabled = false;
        private List<String> atMobiles;
    }
    
    /**
     * 企业微信配置
     */
    @Data
    public static class WeChatWorkConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String corpId;
        private String corpSecret;
        private String agentId;
        private String webhookUrl;
        private int timeoutMs = 5000;
    }
    
    /**
     * Webhook配置
     */
    @Data
    public static class WebhookConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private int timeoutMs = 10000;
        private int maxRetries = 3;
        private long retryIntervalMs = 2000L;
        private Map<String, String> defaultHeaders;
        private String contentType = "application/json";
    }
    
    /**
     * 创建默认配置
     * 
     * @return 默认配置
     */
    public static SubscriptionConfig createDefault() {
        SubscriptionConfig config = new SubscriptionConfig();
        
        // 初始化渠道配置
        config.setEmailConfig(new EmailConfig());
        config.setSmsConfig(new SmsConfig());
        config.setDingTalkConfig(new DingTalkConfig());
        config.setWeChatWorkConfig(new WeChatWorkConfig());
        config.setWebhookConfig(new WebhookConfig());
        
        log.info("创建默认订阅配置");
        return config;
    }
    
    /**
     * 创建高性能配置
     * 
     * @return 高性能配置
     */
    public static SubscriptionConfig createHighPerformance() {
        SubscriptionConfig config = createDefault();
        
        // 优化性能参数
        config.setParallelism(4);
        config.setAsyncNotificationEnabled(true);
        config.setNotificationQueueSize(20000);
        config.setSubscriptionCacheEnabled(true);
        config.setSubscriptionCacheMaxSize(10000);
        
        // 优化超时设置
        config.setMatchTimeoutMs(2000L);
        config.setNotificationTimeoutMs(15000L);
        
        // 优化统计间隔
        config.setStatisticsIntervalSeconds(30);
        config.setConfigRefreshIntervalSeconds(180);
        
        log.info("创建高性能订阅配置");
        return config;
    }
    
    /**
     * 创建安全优先配置
     * 
     * @return 安全优先配置
     */
    public static SubscriptionConfig createSecurityFocused() {
        SubscriptionConfig config = createDefault();
        
        // 启用安全功能
        config.setSubscriptionAuthEnabled(true);
        config.setNotificationContentFilterEnabled(true);
        config.setSubscriptionPersistenceEnabled(true);
        
        // 限制资源使用
        config.setMaxSubscriptions(5000);
        config.setMaxUsers(500);
        config.setNotificationQueueSize(5000);
        
        // 增强监控
        config.setVerboseLoggingEnabled(true);
        config.setStatisticsEnabled(true);
        
        log.info("创建安全优先订阅配置");
        return config;
    }
    
    /**
     * 验证配置参数
     * 
     * @return 是否有效
     */
    public boolean validate() {
        boolean valid = true;
        
        if (parallelism <= 0) {
            log.error("并行度必须大于0: {}", parallelism);
            valid = false;
        }
        
        if (maxSubscriptions <= 0) {
            log.error("最大订阅数必须大于0: {}", maxSubscriptions);
            valid = false;
        }
        
        if (notificationQueueSize <= 0) {
            log.error("通知队列大小必须大于0: {}", notificationQueueSize);
            valid = false;
        }
        
        if (notificationTimeoutMs <= 0) {
            log.error("通知超时时间必须大于0: {}", notificationTimeoutMs);
            valid = false;
        }
        
        if (statisticsIntervalSeconds <= 0) {
            log.error("统计间隔必须大于0: {}", statisticsIntervalSeconds);
            valid = false;
        }
        
        return valid;
    }
    
    /**
     * 打印配置信息
     */
    public void printConfig() {
        log.info("=== 订阅配置信息 ===");
        log.info("基础配置:");
        log.info("  启用状态: {}", enabled);
        log.info("  透传告警: {}", passthroughAlarms);
        log.info("  并行度: {}", parallelism);
        log.info("  最大订阅数: {}", maxSubscriptions);
        
        log.info("匹配配置:");
        log.info("  匹配超时: {}ms", matchTimeoutMs);
        log.info("  缓存启用: {}", subscriptionCacheEnabled);
        log.info("  缓存过期: {}ms", subscriptionCacheExpirationMs);
        
        log.info("通知配置:");
        log.info("  异步通知: {}", asyncNotificationEnabled);
        log.info("  队列大小: {}", notificationQueueSize);
        log.info("  通知超时: {}ms", notificationTimeoutMs);
        log.info("  重试次数: {}", notificationRetryCount);
        
        log.info("频率控制:");
        log.info("  频率控制启用: {}", frequencyControlEnabled);
        log.info("  默认间隔: {}分钟", defaultNotificationIntervalMinutes);
        log.info("  每日最大通知: {}", defaultMaxNotificationsPerDay);
        
        log.info("渠道配置:");
        log.info("  邮件: {}", emailConfig != null ? emailConfig.isEnabled() : false);
        log.info("  短信: {}", smsConfig != null ? smsConfig.isEnabled() : false);
        log.info("  钉钉: {}", dingTalkConfig != null ? dingTalkConfig.isEnabled() : false);
        log.info("  企业微信: {}", weChatWorkConfig != null ? weChatWorkConfig.isEnabled() : false);
        
        log.info("=== 配置信息完成 ===");
    }
}
