package com.geeksec.sessionthreatdetector.model.input;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * TCP协议相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TcpInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * TCP标志位
     */
    private String flags;

    /**
     * 窗口大小
     */
    private Integer windowSize;

    /**
     * 序列号
     */
    private Long sequenceNumber;

    /**
     * 确认号
     */
    private Long acknowledgmentNumber;

    /**
     * 连接状态
     */
    private String connectionState;

    /**
     * 连接持续时间（毫秒）
     */
    private Long connectionDuration;

    /**
     * 重传次数
     */
    private Integer retransmissions;

    /**
     * 往返时间（RTT）
     */
    private Double rtt;

    /**
     * 负载数据
     */
    private byte[] payload;

    /**
     * 负载长度
     */
    private Integer payloadLength;

    /**
     * 是否包含负载
     */
    private Boolean hasPayload;

    /**
     * 连接建立时间
     */
    private Long establishTime;

    /**
     * 连接关闭时间
     */
    private Long closeTime;
}
