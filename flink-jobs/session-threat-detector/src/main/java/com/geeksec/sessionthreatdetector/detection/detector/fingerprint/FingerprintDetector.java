package com.geeksec.sessionthreatdetector.detection.detector.fingerprint;

import com.geeksec.sessionthreatdetector.detection.DetectorType;
import com.geeksec.sessionthreatdetector.detection.ThreatDetector;
import com.geeksec.sessionthreatdetector.model.detection.DetectionResult;
import com.geeksec.sessionthreatdetector.model.input.NetworkEvent;
import com.geeksec.sessionthreatdetector.model.input.HttpInfo;
import com.geeksec.sessionthreatdetector.model.input.SslInfo;

import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 指纹检测器
 * 检测各种恶意软件、木马、渗透工具的指纹特征
 *
 * <AUTHOR>
 */
@Slf4j
public class FingerprintDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 木马指纹库
    private static final Map<String, String> TROJAN_FINGERPRINTS = new HashMap<>();
    
    // 恶意软件指纹库
    private static final Map<String, String> MALWARE_FINGERPRINTS = new HashMap<>();
    
    // 渗透工具指纹库
    private static final Map<String, String> PENETRATION_TOOL_FINGERPRINTS = new HashMap<>();
    
    // 匿名通讯指纹库
    private static final Map<String, String> ANONYMOUS_COMM_FINGERPRINTS = new HashMap<>();
    
    // 爬虫工具指纹库
    private static final Map<String, String> CRAWLER_FINGERPRINTS = new HashMap<>();

    static {
        initializeTrojanFingerprints();
        initializeMalwareFingerprints();
        initializePenetrationToolFingerprints();
        initializeAnonymousCommFingerprints();
        initializeCrawlerFingerprints();
    }

    private static void initializeTrojanFingerprints() {
        // 常见木马指纹
        TROJAN_FINGERPRINTS.put("trojan_ja3_1", "远控木马A");
        TROJAN_FINGERPRINTS.put("trojan_ja3_2", "远控木马B");
        TROJAN_FINGERPRINTS.put("trojan_ua_1", "木马User-Agent");
        
        log.info("初始化木马指纹库，共 {} 个指纹", TROJAN_FINGERPRINTS.size());
    }

    private static void initializeMalwareFingerprints() {
        // 常见恶意软件指纹
        MALWARE_FINGERPRINTS.put("malware_ja3_1", "恶意软件A");
        MALWARE_FINGERPRINTS.put("malware_ja3_2", "恶意软件B");
        MALWARE_FINGERPRINTS.put("malware_ua_1", "恶意软件User-Agent");
        
        log.info("初始化恶意软件指纹库，共 {} 个指纹", MALWARE_FINGERPRINTS.size());
    }

    private static void initializePenetrationToolFingerprints() {
        // 常见渗透工具指纹
        PENETRATION_TOOL_FINGERPRINTS.put("nmap", "Nmap扫描工具");
        PENETRATION_TOOL_FINGERPRINTS.put("sqlmap", "SQLMap注入工具");
        PENETRATION_TOOL_FINGERPRINTS.put("burpsuite", "BurpSuite渗透工具");
        PENETRATION_TOOL_FINGERPRINTS.put("nikto", "Nikto扫描工具");
        PENETRATION_TOOL_FINGERPRINTS.put("dirb", "目录扫描工具");
        PENETRATION_TOOL_FINGERPRINTS.put("gobuster", "目录爆破工具");
        PENETRATION_TOOL_FINGERPRINTS.put("wpscan", "WordPress扫描工具");
        
        log.info("初始化渗透工具指纹库，共 {} 个指纹", PENETRATION_TOOL_FINGERPRINTS.size());
    }

    private static void initializeAnonymousCommFingerprints() {
        // 匿名通讯工具指纹
        ANONYMOUS_COMM_FINGERPRINTS.put("tor_ja3_1", "Tor匿名网络");
        ANONYMOUS_COMM_FINGERPRINTS.put("i2p_ja3_1", "I2P匿名网络");
        ANONYMOUS_COMM_FINGERPRINTS.put("proxy_ua_1", "代理工具");
        
        log.info("初始化匿名通讯指纹库，共 {} 个指纹", ANONYMOUS_COMM_FINGERPRINTS.size());
    }

    private static void initializeCrawlerFingerprints() {
        // 爬虫工具指纹
        CRAWLER_FINGERPRINTS.put("scrapy", "Scrapy爬虫框架");
        CRAWLER_FINGERPRINTS.put("requests", "Python Requests库");
        CRAWLER_FINGERPRINTS.put("curl", "cURL工具");
        CRAWLER_FINGERPRINTS.put("wget", "Wget工具");
        CRAWLER_FINGERPRINTS.put("python-urllib", "Python urllib库");
        
        log.info("初始化爬虫工具指纹库，共 {} 个指纹", CRAWLER_FINGERPRINTS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.FINGERPRINT;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 1. HTTP指纹检测
            if (event.getEventType() == NetworkEvent.EventType.HTTP && event.getHttpInfo() != null) {
                DetectionResult httpResult = detectHttpFingerprint(event);
                if (httpResult != null) {
                    results.add(httpResult);
                }
            }

            // 2. SSL指纹检测
            if (event.getEventType() == NetworkEvent.EventType.SSL && event.getSslInfo() != null) {
                DetectionResult sslResult = detectSslFingerprint(event);
                if (sslResult != null) {
                    results.add(sslResult);
                }
            }

        } catch (Exception e) {
            log.error("指纹检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测HTTP指纹
     */
    private DetectionResult detectHttpFingerprint(NetworkEvent event) {
        HttpInfo httpInfo = event.getHttpInfo();
        String userAgent = httpInfo.getUserAgent();
        
        if (userAgent == null) {
            return null;
        }

        String lowerUserAgent = userAgent.toLowerCase();

        // 检测渗透工具指纹
        for (Map.Entry<String, String> entry : PENETRATION_TOOL_FINGERPRINTS.entrySet()) {
            if (lowerUserAgent.contains(entry.getKey().toLowerCase())) {
                return createDetectionResult(event, "PENETRATION_TOOL_UA", 
                        "渗透工具指纹",
                        DetectionResult.ThreatLevel.HIGH, 0.9,
                        String.format("检测到%s，User-Agent: %s", entry.getValue(), userAgent));
            }
        }

        // 检测爬虫工具指纹
        for (Map.Entry<String, String> entry : CRAWLER_FINGERPRINTS.entrySet()) {
            if (lowerUserAgent.contains(entry.getKey().toLowerCase())) {
                return createDetectionResult(event, "CRAWLER_TOOL_UA", 
                        "爬虫工具指纹",
                        DetectionResult.ThreatLevel.MEDIUM, 0.7,
                        String.format("检测到%s，User-Agent: %s", entry.getValue(), userAgent));
            }
        }

        // 检测木马User-Agent指纹
        for (Map.Entry<String, String> entry : TROJAN_FINGERPRINTS.entrySet()) {
            if (entry.getKey().startsWith("trojan_ua_") && lowerUserAgent.contains(entry.getKey())) {
                return createDetectionResult(event, "TROJAN_UA", 
                        "木马指纹",
                        DetectionResult.ThreatLevel.HIGH, 0.95,
                        String.format("检测到%s，User-Agent: %s", entry.getValue(), userAgent));
            }
        }

        // 检测恶意软件User-Agent指纹
        for (Map.Entry<String, String> entry : MALWARE_FINGERPRINTS.entrySet()) {
            if (entry.getKey().startsWith("malware_ua_") && lowerUserAgent.contains(entry.getKey())) {
                return createDetectionResult(event, "MALWARE_UA", 
                        "恶意软件指纹",
                        DetectionResult.ThreatLevel.HIGH, 0.9,
                        String.format("检测到%s，User-Agent: %s", entry.getValue(), userAgent));
            }
        }

        return null;
    }

    /**
     * 检测SSL指纹
     */
    private DetectionResult detectSslFingerprint(NetworkEvent event) {
        SslInfo sslInfo = event.getSslInfo();
        String ja3Hash = sslInfo.getJa3Hash();
        
        if (ja3Hash == null) {
            return null;
        }

        // 检测木马JA3指纹
        if (TROJAN_FINGERPRINTS.containsKey(ja3Hash)) {
            return createDetectionResult(event, "TROJAN_JA3", 
                    "木马指纹",
                    DetectionResult.ThreatLevel.HIGH, 0.95,
                    String.format("检测到%s，JA3: %s", TROJAN_FINGERPRINTS.get(ja3Hash), ja3Hash));
        }

        // 检测恶意软件JA3指纹
        if (MALWARE_FINGERPRINTS.containsKey(ja3Hash)) {
            return createDetectionResult(event, "MALWARE_JA3", 
                    "恶意软件指纹",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    String.format("检测到%s，JA3: %s", MALWARE_FINGERPRINTS.get(ja3Hash), ja3Hash));
        }

        // 检测匿名通讯JA3指纹
        if (ANONYMOUS_COMM_FINGERPRINTS.containsKey(ja3Hash)) {
            return createDetectionResult(event, "ANONYMOUS_COMM_JA3", 
                    "匿名通讯指纹",
                    DetectionResult.ThreatLevel.MEDIUM, 0.8,
                    String.format("检测到%s，JA3: %s", ANONYMOUS_COMM_FINGERPRINTS.get(ja3Hash), ja3Hash));
        }

        return null;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String ruleId, String threatType,
                                                  DetectionResult.ThreatLevel level, double confidence, String description) {
        return DetectionResult.builder()
                .detectorType(getDetectorType())
                .ruleId(ruleId)
                .threatType(threatType)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(event.getTimestamp())
                .build();
    }
}
