package com.geeksec.sessionthreatdetector.subscription.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 订阅规则模型
 * 定义告警匹配的具体规则
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionRule implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则类型
     */
    private RuleType ruleType;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 操作符
     */
    private Operator operator;
    
    /**
     * 匹配值
     */
    private Object value;
    
    /**
     * 匹配值列表（用于IN、NOT_IN操作）
     */
    private List<Object> values;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 规则类型枚举
     */
    public enum RuleType {
        /** 字段匹配规则 */
        FIELD_MATCH,
        /** 正则表达式规则 */
        REGEX,
        /** 范围规则 */
        RANGE,
        /** 时间规则 */
        TIME,
        /** 自定义规则 */
        CUSTOM
    }
    
    /**
     * 操作符枚举
     */
    public enum Operator {
        /** 等于 */
        EQUALS,
        /** 不等于 */
        NOT_EQUALS,
        /** 包含 */
        CONTAINS,
        /** 不包含 */
        NOT_CONTAINS,
        /** 开始于 */
        STARTS_WITH,
        /** 结束于 */
        ENDS_WITH,
        /** 在列表中 */
        IN,
        /** 不在列表中 */
        NOT_IN,
        /** 大于 */
        GREATER_THAN,
        /** 大于等于 */
        GREATER_THAN_OR_EQUAL,
        /** 小于 */
        LESS_THAN,
        /** 小于等于 */
        LESS_THAN_OR_EQUAL,
        /** 正则匹配 */
        REGEX_MATCH,
        /** 为空 */
        IS_NULL,
        /** 不为空 */
        IS_NOT_NULL,
        /** 存在 */
        EXISTS,
        /** 不存在 */
        NOT_EXISTS
    }
    
    /**
     * 检查规则是否匹配告警
     * 
     * @param alarm 告警对象
     * @return 是否匹配
     */
    public boolean matches(Object alarm) {
        if (!Boolean.TRUE.equals(enabled)) {
            return true; // 禁用的规则默认匹配
        }
        
        try {
            Object fieldValue = getFieldValue(alarm, fieldName);
            return evaluateCondition(fieldValue);
        } catch (Exception e) {
            // 异常情况下默认不匹配
            return false;
        }
    }
    
    /**
     * 获取字段值
     * 
     * @param alarm 告警对象
     * @param fieldName 字段名称
     * @return 字段值
     */
    private Object getFieldValue(Object alarm, String fieldName) {
        if (alarm == null || fieldName == null) {
            return null;
        }
        
        try {
            // 使用反射获取字段值
            java.lang.reflect.Field field = alarm.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(alarm);
        } catch (Exception e) {
            // 尝试使用getter方法
            try {
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                java.lang.reflect.Method getter = alarm.getClass().getMethod(getterName);
                return getter.invoke(alarm);
            } catch (Exception ex) {
                return null;
            }
        }
    }
    
    /**
     * 评估条件
     * 
     * @param fieldValue 字段值
     * @return 是否匹配
     */
    private boolean evaluateCondition(Object fieldValue) {
        switch (operator) {
            case EQUALS:
                return equals(fieldValue, value);
            case NOT_EQUALS:
                return !equals(fieldValue, value);
            case CONTAINS:
                return contains(fieldValue, value);
            case NOT_CONTAINS:
                return !contains(fieldValue, value);
            case STARTS_WITH:
                return startsWith(fieldValue, value);
            case ENDS_WITH:
                return endsWith(fieldValue, value);
            case IN:
                return in(fieldValue, values);
            case NOT_IN:
                return !in(fieldValue, values);
            case GREATER_THAN:
                return greaterThan(fieldValue, value);
            case GREATER_THAN_OR_EQUAL:
                return greaterThanOrEqual(fieldValue, value);
            case LESS_THAN:
                return lessThan(fieldValue, value);
            case LESS_THAN_OR_EQUAL:
                return lessThanOrEqual(fieldValue, value);
            case REGEX_MATCH:
                return regexMatch(fieldValue, value);
            case IS_NULL:
                return fieldValue == null;
            case IS_NOT_NULL:
                return fieldValue != null;
            case EXISTS:
                return fieldValue != null;
            case NOT_EXISTS:
                return fieldValue == null;
            default:
                return false;
        }
    }
    
    /**
     * 相等比较
     */
    private boolean equals(Object fieldValue, Object targetValue) {
        if (fieldValue == null && targetValue == null) {
            return true;
        }
        if (fieldValue == null || targetValue == null) {
            return false;
        }
        return fieldValue.toString().equals(targetValue.toString());
    }
    
    /**
     * 包含比较
     */
    private boolean contains(Object fieldValue, Object targetValue) {
        if (fieldValue == null || targetValue == null) {
            return false;
        }
        return fieldValue.toString().contains(targetValue.toString());
    }
    
    /**
     * 开始于比较
     */
    private boolean startsWith(Object fieldValue, Object targetValue) {
        if (fieldValue == null || targetValue == null) {
            return false;
        }
        return fieldValue.toString().startsWith(targetValue.toString());
    }
    
    /**
     * 结束于比较
     */
    private boolean endsWith(Object fieldValue, Object targetValue) {
        if (fieldValue == null || targetValue == null) {
            return false;
        }
        return fieldValue.toString().endsWith(targetValue.toString());
    }
    
    /**
     * 在列表中比较
     */
    private boolean in(Object fieldValue, List<Object> targetValues) {
        if (fieldValue == null || targetValues == null || targetValues.isEmpty()) {
            return false;
        }
        
        String fieldStr = fieldValue.toString();
        return targetValues.stream()
                .anyMatch(val -> val != null && fieldStr.equals(val.toString()));
    }
    
    /**
     * 大于比较
     */
    private boolean greaterThan(Object fieldValue, Object targetValue) {
        return compareNumbers(fieldValue, targetValue) > 0;
    }
    
    /**
     * 大于等于比较
     */
    private boolean greaterThanOrEqual(Object fieldValue, Object targetValue) {
        return compareNumbers(fieldValue, targetValue) >= 0;
    }
    
    /**
     * 小于比较
     */
    private boolean lessThan(Object fieldValue, Object targetValue) {
        return compareNumbers(fieldValue, targetValue) < 0;
    }
    
    /**
     * 小于等于比较
     */
    private boolean lessThanOrEqual(Object fieldValue, Object targetValue) {
        return compareNumbers(fieldValue, targetValue) <= 0;
    }
    
    /**
     * 数字比较
     */
    private int compareNumbers(Object fieldValue, Object targetValue) {
        if (fieldValue == null || targetValue == null) {
            return 0;
        }
        
        try {
            double field = Double.parseDouble(fieldValue.toString());
            double target = Double.parseDouble(targetValue.toString());
            return Double.compare(field, target);
        } catch (NumberFormatException e) {
            // 如果不是数字，则按字符串比较
            return fieldValue.toString().compareTo(targetValue.toString());
        }
    }
    
    /**
     * 正则匹配
     */
    private boolean regexMatch(Object fieldValue, Object targetValue) {
        if (fieldValue == null || targetValue == null) {
            return false;
        }
        
        try {
            Pattern pattern = Pattern.compile(targetValue.toString());
            return pattern.matcher(fieldValue.toString()).matches();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 验证规则配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        
        if (operator == null) {
            return false;
        }
        
        // 检查操作符对应的值是否正确配置
        switch (operator) {
            case IN:
            case NOT_IN:
                return values != null && !values.isEmpty();
            case IS_NULL:
            case IS_NOT_NULL:
            case EXISTS:
            case NOT_EXISTS:
                return true; // 这些操作符不需要值
            default:
                return value != null;
        }
    }
    
    /**
     * 创建字段匹配规则
     * 
     * @param fieldName 字段名称
     * @param operator 操作符
     * @param value 匹配值
     * @return 订阅规则
     */
    public static SubscriptionRule createFieldRule(String fieldName, Operator operator, Object value) {
        return SubscriptionRule.builder()
                .ruleType(RuleType.FIELD_MATCH)
                .fieldName(fieldName)
                .operator(operator)
                .value(value)
                .enabled(true)
                .build();
    }
    
    /**
     * 创建正则匹配规则
     * 
     * @param fieldName 字段名称
     * @param regex 正则表达式
     * @return 订阅规则
     */
    public static SubscriptionRule createRegexRule(String fieldName, String regex) {
        return SubscriptionRule.builder()
                .ruleType(RuleType.REGEX)
                .fieldName(fieldName)
                .operator(Operator.REGEX_MATCH)
                .value(regex)
                .enabled(true)
                .build();
    }
    
    /**
     * 创建范围规则
     * 
     * @param fieldName 字段名称
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 订阅规则列表
     */
    public static List<SubscriptionRule> createRangeRules(String fieldName, Object minValue, Object maxValue) {
        List<SubscriptionRule> rules = new java.util.ArrayList<>();
        
        if (minValue != null) {
            rules.add(SubscriptionRule.builder()
                    .ruleType(RuleType.RANGE)
                    .fieldName(fieldName)
                    .operator(Operator.GREATER_THAN_OR_EQUAL)
                    .value(minValue)
                    .enabled(true)
                    .build());
        }
        
        if (maxValue != null) {
            rules.add(SubscriptionRule.builder()
                    .ruleType(RuleType.RANGE)
                    .fieldName(fieldName)
                    .operator(Operator.LESS_THAN_OR_EQUAL)
                    .value(maxValue)
                    .enabled(true)
                    .build());
        }
        
        return rules;
    }
}
