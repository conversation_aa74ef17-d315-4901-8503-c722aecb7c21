package com.geeksec.sessionthreatdetector.detection;

import com.geeksec.sessionthreatdetector.model.detection.DetectionResult;
import com.geeksec.sessionthreatdetector.model.input.NetworkEvent;

import java.io.Serializable;
import java.util.List;

/**
 * 威胁检测器统一接口
 * 所有威胁检测器都需要实现此接口，提供统一的检测方法
 *
 * <AUTHOR>
 */
public interface ThreatDetector extends Serializable {

    /**
     * 获取检测器类型
     *
     * @return 检测器类型
     */
    DetectorType getDetectorType();

    /**
     * 检测网络事件是否包含威胁
     *
     * @param event 网络事件
     * @return 检测结果列表，如果没有检测到威胁则返回空列表
     */
    List<DetectionResult> detect(NetworkEvent event);

    /**
     * 检测器是否启用
     *
     * @return true表示启用，false表示禁用
     */
    default boolean isEnabled() {
        return true;
    }

    /**
     * 获取检测器优先级
     * 数值越小优先级越高
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
