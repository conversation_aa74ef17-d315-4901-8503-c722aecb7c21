package com.geeksec.sessionthreatdetector.detection.detector.geo;

import com.geeksec.sessionthreatdetector.detection.DetectorType;
import com.geeksec.sessionthreatdetector.detection.ThreatDetector;
import com.geeksec.sessionthreatdetector.model.detection.DetectionResult;
import com.geeksec.sessionthreatdetector.model.input.HttpInfo;
import com.geeksec.sessionthreatdetector.model.input.NetworkEvent;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 地理位置异常检测器
 * 检测异常的地理位置访问模式和代理隧道
 *
 * <AUTHOR>
 */
@Slf4j
public class GeoAnomalyDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 熵值阈值
    private static final double ENTROPY_THRESHOLD = 3.5;

    // 可疑的地理位置相关头部字段
    private static final Set<String> GEO_HEADERS = new HashSet<>(Arrays.asList(
            "x-forwarded-for", "x-real-ip", "x-originating-ip", "x-remote-ip",
            "x-client-ip", "x-forwarded", "forwarded-for", "forwarded",
            "via", "x-cluster-client-ip", "x-azure-clientip", "x-azure-socketip"
    ));

    // 已知代理工具的特征
    private static final Map<String, String> PROXY_SIGNATURES = new HashMap<>();

    // 英文单词列表（用于熵值计算时排除常见单词）
    private static final Set<String> ENGLISH_WORDS = new HashSet<>(Arrays.asList(
            "the", "and", "for", "are", "but", "not", "you", "all", "can", "had", "her", "was", "one", "our", "out", "day", "get", "has", "him", "his", "how", "man", "new", "now", "old", "see", "two", "way", "who", "boy", "did", "its", "let", "put", "say", "she", "too", "use"
    ));

    static {
        initializeProxySignatures();
    }

    private static void initializeProxySignatures() {
        // Neoreg代理隧道特征
        PROXY_SIGNATURES.put("neoreg", "Neoreg代理隧道");
        PROXY_SIGNATURES.put("regeorg", "Regeorg代理隧道");
        PROXY_SIGNATURES.put("abptts", "ABPTTS代理隧道");
        PROXY_SIGNATURES.put("tunna", "Tunna代理隧道");
        
        log.info("初始化代理工具特征库，共 {} 个特征", PROXY_SIGNATURES.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.GEO_ANOMALY;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 只处理HTTP事件
            if (event.getEventType() != NetworkEvent.EventType.HTTP || event.getHttpInfo() == null) {
                return results;
            }

            HttpInfo httpInfo = event.getHttpInfo();

            // 1. 检测异常的地理位置头部
            DetectionResult geoHeaderResult = detectAbnormalGeoHeaders(event, httpInfo);
            if (geoHeaderResult != null) {
                results.add(geoHeaderResult);
            }

            // 2. 检测代理隧道特征
            DetectionResult proxyResult = detectProxyTunnel(event, httpInfo);
            if (proxyResult != null) {
                results.add(proxyResult);
            }

            // 3. 检测IP地址异常
            DetectionResult ipAnomalyResult = detectIpAnomaly(event);
            if (ipAnomalyResult != null) {
                results.add(ipAnomalyResult);
            }

        } catch (Exception e) {
            log.error("地理位置异常检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测异常的地理位置头部
     */
    private DetectionResult detectAbnormalGeoHeaders(NetworkEvent event, HttpInfo httpInfo) {
        Map<String, String> requestHeaders = httpInfo.getRequestHeaders();
        if (requestHeaders == null || requestHeaders.isEmpty()) {
            return null;
        }

        // 特殊处理：检测Neoregeo代理隧道特征（基于旧版本逻辑）
        DetectionResult neoregeoResult = detectNeoregeoTunnel(event, requestHeaders);
        if (neoregeoResult != null) {
            return neoregeoResult;
        }

        for (Map.Entry<String, String> header : requestHeaders.entrySet()) {
            String headerName = header.getKey().toLowerCase();
            String headerValue = header.getValue();

            // 检查是否为地理位置相关头部
            if (GEO_HEADERS.contains(headerName) && headerValue != null) {

                // 计算头部值的熵值
                double entropy = calculateEntropy(headerValue);

                // 检查是否为高熵值且不是常见英文单词
                if (entropy >= ENTROPY_THRESHOLD && !isCommonEnglishWord(headerValue.toLowerCase())) {
                    return createDetectionResult(event, "GEO_ABNORMAL_HEADER",
                            "异常地理位置头部",
                            DetectionResult.ThreatLevel.MEDIUM, 0.7,
                            String.format("异常头部: %s, 值: %s, 熵值: %.2f",
                                    headerName, headerValue, entropy));
                }

                // 检查是否包含多个IP地址（可能的代理链）
                if (containsMultipleIps(headerValue)) {
                    return createDetectionResult(event, "GEO_PROXY_CHAIN",
                            "代理链检测",
                            DetectionResult.ThreatLevel.MEDIUM, 0.6,
                            String.format("代理链头部: %s, 值: %s", headerName, headerValue));
                }

                // 检查是否包含内网IP（可能的内网代理）
                if (containsPrivateIp(headerValue)) {
                    return createDetectionResult(event, "GEO_PRIVATE_IP_PROXY",
                            "内网IP代理",
                            DetectionResult.ThreatLevel.LOW, 0.5,
                            String.format("内网IP头部: %s, 值: %s", headerName, headerValue));
                }
            }
        }

        return null;
    }

    /**
     * 检测Neoregeo代理隧道（基于旧版本逻辑）
     */
    private DetectionResult detectNeoregeoTunnel(NetworkEvent event, Map<String, String> requestHeaders) {
        // 检查所有请求头中的异常字段
        for (Map.Entry<String, String> header : requestHeaders.entrySet()) {
            String headerName = header.getKey();
            String headerValue = header.getValue();

            // 检查头部名称是否包含非英文字段（类似旧版本的逻辑）
            if (headerName.contains("-")) {
                String[] parts = headerName.split("-");
                if (parts.length > 1) {
                    String field = parts[1];

                    // 检查字段是否不在英文单词列表中（简化版本的检查）
                    if (!isCommonEnglishWord(field.toLowerCase()) &&
                        field.length() > 3 &&
                        !isStandardHttpHeader(field)) {

                        return createDetectionResult(event, "NEOREG_TUNNEL",
                                "Neoregeo代理隧道",
                                DetectionResult.ThreatLevel.HIGH, 0.9,
                                String.format("检测到Neoregeo代理隧道特征: 异常头部字段=%s, 值=%s",
                                        field, headerValue));
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检查是否为标准HTTP头部字段
     */
    private boolean isStandardHttpHeader(String field) {
        Set<String> standardHeaders = Set.of(
                "accept", "authorization", "cache", "connection", "content", "cookie",
                "date", "expect", "from", "host", "if", "max", "pragma", "proxy",
                "range", "referer", "te", "upgrade", "user", "via", "warning"
        );

        String lowerField = field.toLowerCase();
        return standardHeaders.stream().anyMatch(lowerField::contains);
    }

    /**
     * 检测代理隧道特征
     */
    private DetectionResult detectProxyTunnel(NetworkEvent event, HttpInfo httpInfo) {
        String uri = httpInfo.getUri();
        String userAgent = httpInfo.getUserAgent();
        String requestBody = httpInfo.getRequestBody();

        // 检查URI中的代理工具特征
        if (uri != null) {
            String lowerUri = uri.toLowerCase();
            for (Map.Entry<String, String> entry : PROXY_SIGNATURES.entrySet()) {
                if (lowerUri.contains(entry.getKey())) {
                    return createDetectionResult(event, "GEO_PROXY_TOOL", 
                            entry.getValue(),
                            DetectionResult.ThreatLevel.HIGH, 0.9,
                            "URI中检测到代理工具特征: " + entry.getKey());
                }
            }
        }

        // 检查User-Agent中的代理工具特征
        if (userAgent != null) {
            String lowerUA = userAgent.toLowerCase();
            for (Map.Entry<String, String> entry : PROXY_SIGNATURES.entrySet()) {
                if (lowerUA.contains(entry.getKey())) {
                    return createDetectionResult(event, "GEO_PROXY_TOOL_UA", 
                            entry.getValue(),
                            DetectionResult.ThreatLevel.HIGH, 0.8,
                            "User-Agent中检测到代理工具特征: " + entry.getKey());
                }
            }
        }

        // 检查请求体中的代理工具特征
        if (requestBody != null) {
            String lowerBody = requestBody.toLowerCase();
            for (Map.Entry<String, String> entry : PROXY_SIGNATURES.entrySet()) {
                if (lowerBody.contains(entry.getKey())) {
                    return createDetectionResult(event, "GEO_PROXY_TOOL_BODY", 
                            entry.getValue(),
                            DetectionResult.ThreatLevel.HIGH, 0.9,
                            "请求体中检测到代理工具特征: " + entry.getKey());
                }
            }
        }

        return null;
    }

    /**
     * 检测IP地址异常
     */
    private DetectionResult detectIpAnomaly(NetworkEvent event) {
        String srcIp = event.getSrcIp();
        String dstIp = event.getDstIp();

        // 检查源IP是否为已知恶意IP（这里简化处理）
        if (srcIp != null && isKnownMaliciousIp(srcIp)) {
            return createDetectionResult(event, "GEO_MALICIOUS_IP", 
                    "恶意IP访问",
                    DetectionResult.ThreatLevel.HIGH, 0.8,
                    "源IP为已知恶意IP: " + srcIp);
        }

        // 检查是否为Tor出口节点（这里简化处理）
        if (srcIp != null && isTorExitNode(srcIp)) {
            return createDetectionResult(event, "GEO_TOR_ACCESS", 
                    "Tor网络访问",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    "源IP为Tor出口节点: " + srcIp);
        }

        return null;
    }

    /**
     * 计算字符串熵值
     */
    private double calculateEntropy(String data) {
        if (data == null || data.isEmpty()) {
            return 0.0;
        }

        Map<Character, Integer> charCount = new HashMap<>();
        for (char c : data.toCharArray()) {
            charCount.put(c, charCount.getOrDefault(c, 0) + 1);
        }

        double entropy = 0.0;
        int length = data.length();
        for (int count : charCount.values()) {
            double probability = (double) count / length;
            entropy -= probability * (Math.log(probability) / Math.log(2));
        }

        return entropy;
    }

    /**
     * 检查是否为常见英文单词
     */
    private boolean isCommonEnglishWord(String word) {
        return ENGLISH_WORDS.contains(word);
    }

    /**
     * 检查是否包含多个IP地址
     */
    private boolean containsMultipleIps(String value) {
        if (value == null) {
            return false;
        }

        // 简单的IP地址正则匹配
        String ipPattern = "\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(ipPattern);
        java.util.regex.Matcher matcher = pattern.matcher(value);

        int ipCount = 0;
        while (matcher.find()) {
            ipCount++;
            if (ipCount > 1) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否包含内网IP
     */
    private boolean containsPrivateIp(String value) {
        if (value == null) {
            return false;
        }

        // 检查常见内网IP段
        return value.contains("10.") || value.contains("192.168.") || 
               value.contains("172.16.") || value.contains("172.17.") ||
               value.contains("172.18.") || value.contains("172.19.") ||
               value.contains("172.20.") || value.contains("172.21.") ||
               value.contains("172.22.") || value.contains("172.23.") ||
               value.contains("172.24.") || value.contains("172.25.") ||
               value.contains("172.26.") || value.contains("172.27.") ||
               value.contains("172.28.") || value.contains("172.29.") ||
               value.contains("172.30.") || value.contains("172.31.");
    }

    /**
     * 检查是否为已知恶意IP（简化实现）
     */
    private boolean isKnownMaliciousIp(String ip) {
        // 这里应该查询威胁情报数据库
        // 简化实现，返回false
        return false;
    }

    /**
     * 检查是否为Tor出口节点（简化实现）
     */
    private boolean isTorExitNode(String ip) {
        // 这里应该查询Tor出口节点列表
        // 简化实现，返回false
        return false;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType, 
                                                String threatName, DetectionResult.ThreatLevel level, 
                                                double confidence, String description) {
        return DetectionResult.builder()
                .detectorName(getDetectorType().getDetectorName())
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("GEO_ANOMALY")
                .assetLabel("SUSPICIOUS_LOCATION")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        return 60; // 较低优先级
    }
}
