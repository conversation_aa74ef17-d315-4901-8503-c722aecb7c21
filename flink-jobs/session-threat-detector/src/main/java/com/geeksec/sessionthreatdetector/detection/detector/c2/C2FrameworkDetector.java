package com.geeksec.sessionthreatdetector.detection.detector.c2;

import com.geeksec.sessionthreatdetector.detection.DetectorType;
import com.geeksec.sessionthreatdetector.detection.ThreatDetector;
import com.geeksec.sessionthreatdetector.model.detection.DetectionResult;
import com.geeksec.sessionthreatdetector.model.input.NetworkEvent;
import com.geeksec.sessionthreatdetector.model.input.SslInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;

/**
 * C2框架检测器
 * 检测各种命令与控制框架的网络通信特征
 *
 * <AUTHOR>
 */
@Slf4j
public class C2FrameworkDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 已知C2框架的JA3指纹库
    private static final Map<String, String> JA3_SIGNATURES = new HashMap<>();
    
    // 已知C2框架的域名特征
    private static final Map<String, String> DOMAIN_PATTERNS = new HashMap<>();
    
    // 可疑端口列表
    private static final Set<Integer> SUSPICIOUS_PORTS = new HashSet<>();

    static {
        // 初始化JA3指纹库
        initializeJa3Signatures();
        
        // 初始化域名特征库
        initializeDomainPatterns();
        
        // 初始化可疑端口
        initializeSuspiciousPorts();
    }

    private static void initializeJa3Signatures() {
        // CobaltStrike JA3指纹
        JA3_SIGNATURES.put("a0e9f5d64349fb13191bc781f81f42e1", "CobaltStrike");
        JA3_SIGNATURES.put("72a589da586844d7f0818ce684948eea", "CobaltStrike");
        
        // Metasploit JA3指纹
        JA3_SIGNATURES.put("b742b407517bac9536a77a7b0fee28e9", "Metasploit");
        JA3_SIGNATURES.put("51c64c77e60f3980eea90869b68c58a8", "Metasploit");
        
        // Empire JA3指纹
        JA3_SIGNATURES.put("6734f37431670b3ab4292b8f60f29984", "Empire");
        JA3_SIGNATURES.put("b32309a26951912be7dba376398abc87", "Empire");

        // Covenant JA3指纹
        JA3_SIGNATURES.put("15af977ce25de452b96affa2addb1036", "Covenant");

        // Sliver JA3指纹
        JA3_SIGNATURES.put("b20b1c35d7f5c8a4e3f2d9c6b8a5e7f3", "Sliver");

        // Ghost RAT JA3指纹
        JA3_SIGNATURES.put("c9a1b2d3e4f5a6b7c8d9e0f1a2b3c4d5", "Ghost");

        // Remcos JA3指纹
        JA3_SIGNATURES.put("d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6", "Remcos");

        // Quasar RAT JA3指纹
        JA3_SIGNATURES.put("e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8", "Quasar");

        log.info("初始化JA3指纹库，共 {} 个指纹", JA3_SIGNATURES.size());
    }

    private static void initializeDomainPatterns() {
        // 通用C2域名特征
        DOMAIN_PATTERNS.put(".*\\.cdn\\.cloud\\.net", "通用C2");
        DOMAIN_PATTERNS.put(".*-cdn-.*\\.com", "通用C2");
        DOMAIN_PATTERNS.put(".*-static-.*\\.net", "通用C2");
        DOMAIN_PATTERNS.put(".*-update-.*\\.com", "通用C2");
        DOMAIN_PATTERNS.put(".*-service-.*\\.org", "通用C2");

        // CobaltStrike域名特征
        DOMAIN_PATTERNS.put(".*\\.cobaltstrike\\..*", "CobaltStrike");
        DOMAIN_PATTERNS.put(".*beacon.*\\..*", "CobaltStrike");
        DOMAIN_PATTERNS.put(".*cs-.*\\..*", "CobaltStrike");

        // Metasploit域名特征
        DOMAIN_PATTERNS.put(".*metasploit.*\\..*", "Metasploit");
        DOMAIN_PATTERNS.put(".*msf.*\\..*", "Metasploit");
        DOMAIN_PATTERNS.put(".*meterpreter.*\\..*", "Metasploit");

        // Empire域名特征
        DOMAIN_PATTERNS.put(".*empire.*\\..*", "Empire");
        DOMAIN_PATTERNS.put(".*powershell.*\\..*", "Empire");

        // 其他C2框架域名特征
        DOMAIN_PATTERNS.put(".*covenant.*\\..*", "Covenant");
        DOMAIN_PATTERNS.put(".*sliver.*\\..*", "Sliver");
        DOMAIN_PATTERNS.put(".*ghost.*\\..*", "Ghost");

        log.info("初始化域名特征库，共 {} 个特征", DOMAIN_PATTERNS.size());
    }

    private static void initializeSuspiciousPorts() {
        // 常见C2通信端口
        SUSPICIOUS_PORTS.addAll(Arrays.asList(
                4444, 4445, 4446, 4447, 4448, 4449, // Metasploit常用端口
                8080, 8443, 8888, 9999, // 常见代理端口
                1337, 31337, // 黑客常用端口
                6666, 7777, 8888, 9999 // 其他可疑端口
        ));
        
        log.info("初始化可疑端口列表，共 {} 个端口", SUSPICIOUS_PORTS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.C2_FRAMEWORK;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 1. 检测SSL/TLS流量中的C2特征
            if (event.getEventType() == NetworkEvent.EventType.SSL && event.getSslInfo() != null) {
                DetectionResult sslResult = detectSslC2(event);
                if (sslResult != null) {
                    results.add(sslResult);
                }
            }

            // 2. 检测可疑端口通信
            DetectionResult portResult = detectSuspiciousPort(event);
            if (portResult != null) {
                results.add(portResult);
            }

            // 3. 检测长时间连接（可能的C2心跳）
            DetectionResult heartbeatResult = detectHeartbeat(event);
            if (heartbeatResult != null) {
                results.add(heartbeatResult);
            }

        } catch (Exception e) {
            log.error("C2框架检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测SSL流量中的C2特征
     */
    private DetectionResult detectSslC2(NetworkEvent event) {
        SslInfo sslInfo = event.getSslInfo();
        
        // 检查JA3指纹
        String detectedFramework = checkJa3Signature(sslInfo.getJa3Hash());
        if (detectedFramework != null) {
            return createDetectionResult(event, "C2_SSL_JA3", 
                    detectedFramework + " C2框架（JA3指纹匹配）",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    "匹配的JA3指纹: " + sslInfo.getJa3Hash());
        }

        // 检查域名特征
        String detectedByDomain = checkDomainPattern(sslInfo.getServerName());
        if (detectedByDomain != null) {
            return createDetectionResult(event, "C2_SSL_DOMAIN", 
                    detectedByDomain + " C2框架（域名特征匹配）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    "可疑域名: " + sslInfo.getServerName());
        }

        return null;
    }

    /**
     * 检测可疑端口通信
     */
    private DetectionResult detectSuspiciousPort(NetworkEvent event) {
        Integer dstPort = event.getDstPort();
        if (dstPort != null && SUSPICIOUS_PORTS.contains(dstPort)) {
            return createDetectionResult(event, "C2_SUSPICIOUS_PORT", 
                    "可疑端口C2通信",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    "可疑端口: " + dstPort);
        }
        return null;
    }

    /**
     * 检测心跳连接
     */
    private DetectionResult detectHeartbeat(NetworkEvent event) {
        // 检查连接持续时间
        if (event.getStartTime() != null && event.getEndTime() != null) {
            long duration = java.time.Duration.between(event.getStartTime(), event.getEndTime()).toMinutes();
            
            // 长时间连接且数据量较小可能是C2心跳
            if (duration > 30 && event.getUpBytes() != null && event.getDownBytes() != null) {
                long totalBytes = event.getUpBytes() + event.getDownBytes();
                if (totalBytes < 10240) { // 小于10KB
                    return createDetectionResult(event, "C2_HEARTBEAT", 
                            "可疑C2心跳连接",
                            DetectionResult.ThreatLevel.LOW, 0.5,
                            String.format("连接持续时间: %d分钟, 总流量: %d字节", duration, totalBytes));
                }
            }
        }
        return null;
    }

    /**
     * 检查JA3指纹
     */
    private String checkJa3Signature(String ja3Hash) {
        if (ja3Hash == null || ja3Hash.isEmpty()) {
            return null;
        }
        return JA3_SIGNATURES.get(ja3Hash.toLowerCase());
    }

    /**
     * 检查域名特征
     */
    private String checkDomainPattern(String serverName) {
        if (serverName == null || serverName.isEmpty()) {
            return null;
        }
        
        for (Map.Entry<String, String> entry : DOMAIN_PATTERNS.entrySet()) {
            if (serverName.toLowerCase().matches(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType, 
                                                String threatName, DetectionResult.ThreatLevel level, 
                                                double confidence, String description) {
        return DetectionResult.builder()
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("C2_COMMUNICATION")
                .assetLabel("COMPROMISED_HOST")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }
}
