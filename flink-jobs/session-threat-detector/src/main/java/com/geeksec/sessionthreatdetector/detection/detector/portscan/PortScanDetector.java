package com.geeksec.sessionthreatdetector.detection.detector.portscan;

import com.geeksec.sessionthreatdetector.detection.DetectorType;
import com.geeksec.sessionthreatdetector.detection.ThreatDetector;
import com.geeksec.sessionthreatdetector.model.detection.DetectionResult;
import com.geeksec.sessionthreatdetector.model.input.NetworkEvent;
import com.geeksec.sessionthreatdetector.model.input.TcpInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 端口扫描检测器
 * 检测各种端口扫描攻击，包括TCP SYN扫描、TCP连接扫描、UDP扫描等
 *
 * <AUTHOR>
 */
@Slf4j
public class PortScanDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 检测阈值
    private static final int PORT_SCAN_THRESHOLD = 20; // 端口扫描阈值
    private static final int HOST_SCAN_THRESHOLD = 10; // 主机扫描阈值
    private static final long TIME_WINDOW_MS = 60000; // 1分钟时间窗口
    private static final int UNIQUE_PORTS_THRESHOLD = 10; // 唯一端口数阈值
    private static final int UNIQUE_HOSTS_THRESHOLD = 5; // 唯一主机数阈值

    // 常见扫描端口
    private static final Set<Integer> COMMON_SCAN_PORTS = new HashSet<>(Arrays.asList(
            21, 22, 23, 25, 53, 80, 110, 111, 135, 139, 143, 443, 993, 995, 1723, 3389, 5900
    ));

    // 状态存储（实际应用中应该使用Flink状态）
    private transient ConcurrentMap<String, ScanStats> scanStats;

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.PORT_SCAN;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 初始化状态存储
            if (scanStats == null) {
                scanStats = new ConcurrentHashMap<>();
            }

            // 只处理TCP和UDP事件
            if (event.getEventType() == NetworkEvent.EventType.TCP) {
                DetectionResult tcpResult = detectTcpPortScan(event);
                if (tcpResult != null) {
                    results.add(tcpResult);
                }
            } else if (event.getEventType() == NetworkEvent.EventType.UDP) {
                DetectionResult udpResult = detectUdpPortScan(event);
                if (udpResult != null) {
                    results.add(udpResult);
                }
            }

            // 检测主机扫描
            DetectionResult hostScanResult = detectHostScan(event);
            if (hostScanResult != null) {
                results.add(hostScanResult);
            }

        } catch (Exception e) {
            log.error("端口扫描检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测TCP端口扫描
     */
    private DetectionResult detectTcpPortScan(NetworkEvent event) {
        TcpInfo tcpInfo = event.getTcpInfo();
        if (tcpInfo == null) {
            return null;
        }

        String srcIp = event.getSrcIp();
        String dstIp = event.getDstIp();
        Integer dstPort = event.getDstPort();

        if (srcIp == null || dstIp == null || dstPort == null) {
            return null;
        }

        // 检查TCP标志位
        String flags = tcpInfo.getFlags();
        if (flags == null) {
            return null;
        }

        // 生成统计键（源IP -> 目标IP）
        String statsKey = srcIp + "->" + dstIp;
        
        // 获取或创建统计信息
        ScanStats stats = scanStats.computeIfAbsent(statsKey, k -> new ScanStats());

        // 更新统计信息
        long currentTime = System.currentTimeMillis();
        stats.addPortAccess(currentTime, dstPort, flags);

        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);

        // 检查SYN扫描特征
        if (flags.toUpperCase().contains("SYN") && !flags.toUpperCase().contains("ACK")) {
            if (stats.getSynCount() >= PORT_SCAN_THRESHOLD) {
                return createDetectionResult(event, "TCP_SYN_SCAN", 
                        "TCP SYN端口扫描",
                        DetectionResult.ThreatLevel.HIGH, 0.9,
                        String.format("SYN包数量: %d, 扫描端口数: %d", 
                                stats.getSynCount(), stats.getUniquePortCount()));
            }
        }

        // 检查连接扫描特征
        if (stats.getUniquePortCount() >= UNIQUE_PORTS_THRESHOLD) {
            return createDetectionResult(event, "TCP_CONNECT_SCAN", 
                    "TCP连接端口扫描",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    String.format("连接尝试数: %d, 扫描端口数: %d", 
                            stats.getConnectionCount(), stats.getUniquePortCount()));
        }

        // 检查常见端口扫描
        if (COMMON_SCAN_PORTS.contains(dstPort) && stats.getCommonPortScanCount() >= 5) {
            return createDetectionResult(event, "COMMON_PORT_SCAN", 
                    "常见端口扫描",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    String.format("常见端口扫描次数: %d", stats.getCommonPortScanCount()));
        }

        return null;
    }

    /**
     * 检测UDP端口扫描
     */
    private DetectionResult detectUdpPortScan(NetworkEvent event) {
        String srcIp = event.getSrcIp();
        String dstIp = event.getDstIp();
        Integer dstPort = event.getDstPort();

        if (srcIp == null || dstIp == null || dstPort == null) {
            return null;
        }

        // 生成统计键（源IP -> 目标IP）
        String statsKey = srcIp + "->" + dstIp + ":UDP";
        
        // 获取或创建统计信息
        ScanStats stats = scanStats.computeIfAbsent(statsKey, k -> new ScanStats());

        // 更新统计信息
        long currentTime = System.currentTimeMillis();
        stats.addPortAccess(currentTime, dstPort, "UDP");

        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);

        // 检查UDP扫描特征
        if (stats.getUniquePortCount() >= UNIQUE_PORTS_THRESHOLD) {
            return createDetectionResult(event, "UDP_PORT_SCAN", 
                    "UDP端口扫描",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    String.format("UDP包数量: %d, 扫描端口数: %d", 
                            stats.getConnectionCount(), stats.getUniquePortCount()));
        }

        return null;
    }

    /**
     * 检测主机扫描
     */
    private DetectionResult detectHostScan(NetworkEvent event) {
        String srcIp = event.getSrcIp();
        Integer dstPort = event.getDstPort();

        if (srcIp == null || dstPort == null) {
            return null;
        }

        // 生成统计键（源IP扫描网段）
        String statsKey = srcIp + ":HOST_SCAN";
        
        // 获取或创建统计信息
        ScanStats stats = scanStats.computeIfAbsent(statsKey, k -> new ScanStats());

        // 更新统计信息
        long currentTime = System.currentTimeMillis();
        stats.addHostAccess(currentTime, event.getDstIp());

        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);

        // 检查主机扫描特征
        if (stats.getUniqueHostCount() >= UNIQUE_HOSTS_THRESHOLD) {
            return createDetectionResult(event, "HOST_SCAN", 
                    "主机扫描",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    String.format("扫描主机数: %d", stats.getUniqueHostCount()));
        }

        return null;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType, 
                                                String threatName, DetectionResult.ThreatLevel level, 
                                                double confidence, String description) {
        return DetectionResult.builder()
                .detectorName(getDetectorType().getDetectorName())
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("PORT_SCAN_ATTACK")
                .assetLabel("SCAN_TARGET")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        return 50; // 中等优先级
    }

    /**
     * 扫描统计信息
     */
    private static class ScanStats {
        private final List<PortAccess> portAccesses = new ArrayList<>();
        private final Set<String> uniqueHosts = new HashSet<>();
        private final List<Long> hostAccessTimes = new ArrayList<>();

        public void addPortAccess(long timestamp, int port, String flags) {
            portAccesses.add(new PortAccess(timestamp, port, flags));
        }

        public void addHostAccess(long timestamp, String host) {
            if (host != null) {
                uniqueHosts.add(host);
                hostAccessTimes.add(timestamp);
            }
        }

        public void cleanExpiredData(long currentTime, long timeWindow) {
            portAccesses.removeIf(access -> currentTime - access.timestamp > timeWindow);
            hostAccessTimes.removeIf(time -> currentTime - time > timeWindow);
            
            // 重新构建主机集合（简化处理）
            // 实际应用中应该更精确地跟踪每个主机访问的时间戳
        }

        public int getSynCount() {
            return (int) portAccesses.stream()
                    .filter(access -> access.flags != null && 
                            access.flags.toUpperCase().contains("SYN") && 
                            !access.flags.toUpperCase().contains("ACK"))
                    .count();
        }

        public int getConnectionCount() {
            return portAccesses.size();
        }

        public int getUniquePortCount() {
            return (int) portAccesses.stream()
                    .mapToInt(access -> access.port)
                    .distinct()
                    .count();
        }

        public int getCommonPortScanCount() {
            return (int) portAccesses.stream()
                    .filter(access -> COMMON_SCAN_PORTS.contains(access.port))
                    .count();
        }

        public int getUniqueHostCount() {
            return uniqueHosts.size();
        }

        private static class PortAccess {
            final long timestamp;
            final int port;
            final String flags;

            PortAccess(long timestamp, int port, String flags) {
                this.timestamp = timestamp;
                this.port = port;
                this.flags = flags;
            }
        }
    }
}
