package com.geeksec.sessionthreatdetector.state.redis;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.geeksec.sessionthreatdetector.killchain.model.AttackChainAnalysis;
import com.geeksec.sessionthreatdetector.killchain.model.AttackChainEvent;
import com.geeksec.sessionthreatdetector.killchain.model.CyberKillChainStage;

import lombok.extern.slf4j.Slf4j;

/**
 * 攻击链状态管理器
 * 负责攻击链相关数据的Redis存储和查询
 * 
 * <AUTHOR>
 */
@Slf4j
public class AttackChainStateManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Redis状态管理器
     */
    private final RedisStateManager redisStateManager;
    
    /**
     * 攻击链过期时间（秒）- 7天
     */
    private static final int ATTACK_CHAIN_TTL = 7 * 24 * 3600;
    
    /**
     * 攻击链事件过期时间（秒）- 3天
     */
    private static final int ATTACK_CHAIN_EVENT_TTL = 3 * 24 * 3600;
    
    /**
     * 活跃攻击链过期时间（秒）- 24小时
     */
    private static final int ACTIVE_CHAIN_TTL = 24 * 3600;
    
    /**
     * 构造函数
     */
    public AttackChainStateManager() {
        this.redisStateManager = RedisStateManager.getInstance();
    }
    
    /**
     * 保存攻击链分析结果
     * 
     * @param analysis 攻击链分析结果
     */
    public void saveAttackChainAnalysis(AttackChainAnalysis analysis) {
        if (analysis == null || analysis.getAttackChainId() == null) {
            return;
        }
        
        try {
            String chainKey = buildAttackChainKey(analysis.getAttackChainId());
            
            // 保存攻击链基本信息
            redisStateManager.setObject(chainKey, analysis, ATTACK_CHAIN_TTL);
            
            // 保存到活跃攻击链集合
            if (analysis.isActiveAttack()) {
                String activeChainKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "active_chains";
                redisStateManager.sadd(activeChainKey, analysis.getAttackChainId());
                redisStateManager.expire(activeChainKey, ACTIVE_CHAIN_TTL);
            }
            
            // 保存攻击链事件
            if (analysis.getEvents() != null) {
                for (AttackChainEvent event : analysis.getEvents()) {
                    saveAttackChainEvent(analysis.getAttackChainId(), event);
                }
            }
            
            // 更新攻击链索引
            updateAttackChainIndexes(analysis);
            
            log.debug("攻击链状态已保存: {}", analysis.getAttackChainId());
            
        } catch (Exception e) {
            log.error("保存攻击链状态失败: {}", analysis.getAttackChainId(), e);
        }
    }
    
    /**
     * 获取攻击链分析结果
     * 
     * @param attackChainId 攻击链ID
     * @return 攻击链分析结果
     */
    public AttackChainAnalysis getAttackChainAnalysis(String attackChainId) {
        if (attackChainId == null) {
            return null;
        }
        
        try {
            String chainKey = buildAttackChainKey(attackChainId);
            return redisStateManager.getObject(chainKey, AttackChainAnalysis.class);
        } catch (Exception e) {
            log.error("获取攻击链状态失败: {}", attackChainId, e);
            return null;
        }
    }
    
    /**
     * 保存攻击链事件
     * 
     * @param attackChainId 攻击链ID
     * @param event 攻击链事件
     */
    public void saveAttackChainEvent(String attackChainId, AttackChainEvent event) {
        if (attackChainId == null || event == null || event.getEventId() == null) {
            return;
        }
        
        try {
            String eventKey = buildAttackChainEventKey(attackChainId, event.getEventId());
            redisStateManager.setObject(eventKey, event, ATTACK_CHAIN_EVENT_TTL);
            
            // 添加到攻击链事件列表
            String eventListKey = buildAttackChainEventListKey(attackChainId);
            redisStateManager.sadd(eventListKey, event.getEventId());
            redisStateManager.expire(eventListKey, ATTACK_CHAIN_TTL);
            
            log.debug("攻击链事件已保存: {} -> {}", attackChainId, event.getEventId());
            
        } catch (Exception e) {
            log.error("保存攻击链事件失败: {} -> {}", attackChainId, event.getEventId(), e);
        }
    }
    
    /**
     * 获取攻击链事件
     * 
     * @param attackChainId 攻击链ID
     * @param eventId 事件ID
     * @return 攻击链事件
     */
    public AttackChainEvent getAttackChainEvent(String attackChainId, String eventId) {
        if (attackChainId == null || eventId == null) {
            return null;
        }
        
        try {
            String eventKey = buildAttackChainEventKey(attackChainId, eventId);
            return redisStateManager.getObject(eventKey, AttackChainEvent.class);
        } catch (Exception e) {
            log.error("获取攻击链事件失败: {} -> {}", attackChainId, eventId, e);
            return null;
        }
    }
    
    /**
     * 获取攻击链的所有事件
     * 
     * @param attackChainId 攻击链ID
     * @return 攻击链事件列表
     */
    public List<AttackChainEvent> getAttackChainEvents(String attackChainId) {
        if (attackChainId == null) {
            return new ArrayList<>();
        }
        
        try {
            String eventListKey = buildAttackChainEventListKey(attackChainId);
            Set<String> eventIds = redisStateManager.smembers(eventListKey);
            
            List<AttackChainEvent> events = new ArrayList<>();
            for (String eventId : eventIds) {
                AttackChainEvent event = getAttackChainEvent(attackChainId, eventId);
                if (event != null) {
                    events.add(event);
                }
            }
            
            // 按时间排序
            events.sort(Comparator.comparing(AttackChainEvent::getTimestamp));
            return events;
            
        } catch (Exception e) {
            log.error("获取攻击链事件列表失败: {}", attackChainId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取活跃攻击链列表
     * 
     * @return 活跃攻击链ID列表
     */
    public List<String> getActiveAttackChains() {
        try {
            String activeChainKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "active_chains";
            Set<String> activeChainIds = redisStateManager.smembers(activeChainKey);
            return new ArrayList<>(activeChainIds);
        } catch (Exception e) {
            log.error("获取活跃攻击链列表失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据攻击者IP查询攻击链
     * 
     * @param attackerIp 攻击者IP
     * @return 攻击链ID列表
     */
    public List<String> getAttackChainsByAttackerIp(String attackerIp) {
        if (attackerIp == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:attacker_ip:" + attackerIp;
            Set<String> chainIds = redisStateManager.smembers(indexKey);
            return new ArrayList<>(chainIds);
        } catch (Exception e) {
            log.error("根据攻击者IP查询攻击链失败: {}", attackerIp, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据受害者IP查询攻击链
     * 
     * @param victimIp 受害者IP
     * @return 攻击链ID列表
     */
    public List<String> getAttackChainsByVictimIp(String victimIp) {
        if (victimIp == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:victim_ip:" + victimIp;
            Set<String> chainIds = redisStateManager.smembers(indexKey);
            return new ArrayList<>(chainIds);
        } catch (Exception e) {
            log.error("根据受害者IP查询攻击链失败: {}", victimIp, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据Kill Chain阶段查询攻击链
     * 
     * @param stage Kill Chain阶段
     * @return 攻击链ID列表
     */
    public List<String> getAttackChainsByStage(CyberKillChainStage stage) {
        if (stage == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:stage:" + stage.getStageCode();
            Set<String> chainIds = redisStateManager.smembers(indexKey);
            return new ArrayList<>(chainIds);
        } catch (Exception e) {
            log.error("根据Kill Chain阶段查询攻击链失败: {}", stage, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据时间范围查询攻击链
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 攻击链ID列表
     */
    public List<String> getAttackChainsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return new ArrayList<>();
        }
        
        try {
            List<String> allChainIds = new ArrayList<>();
            
            // 按天查询索引
            LocalDateTime current = startTime.toLocalDate().atStartOfDay();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            
            while (!current.isAfter(endTime)) {
                String dateKey = current.format(formatter);
                String indexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:date:" + dateKey;
                Set<String> dayChainIds = redisStateManager.smembers(indexKey);
                allChainIds.addAll(dayChainIds);
                current = current.plusDays(1);
            }
            
            return allChainIds.stream().distinct().collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("根据时间范围查询攻击链失败: {} - {}", startTime, endTime, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 删除攻击链
     * 
     * @param attackChainId 攻击链ID
     */
    public void deleteAttackChain(String attackChainId) {
        if (attackChainId == null) {
            return;
        }
        
        try {
            // 删除攻击链基本信息
            String chainKey = buildAttackChainKey(attackChainId);
            redisStateManager.delete(chainKey);
            
            // 删除攻击链事件
            List<AttackChainEvent> events = getAttackChainEvents(attackChainId);
            for (AttackChainEvent event : events) {
                String eventKey = buildAttackChainEventKey(attackChainId, event.getEventId());
                redisStateManager.delete(eventKey);
            }
            
            // 删除事件列表
            String eventListKey = buildAttackChainEventListKey(attackChainId);
            redisStateManager.delete(eventListKey);
            
            // 从活跃攻击链集合中移除
            String activeChainKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "active_chains";
            redisStateManager.srem(activeChainKey, attackChainId);
            
            // 从索引中移除
            removeFromIndexes(attackChainId);
            
            log.debug("攻击链已删除: {}", attackChainId);
            
        } catch (Exception e) {
            log.error("删除攻击链失败: {}", attackChainId, e);
        }
    }
    
    /**
     * 清理过期攻击链
     * 
     * @param maxAgeHours 最大年龄（小时）
     * @return 清理的攻击链数量
     */
    public long cleanupExpiredAttackChains(int maxAgeHours) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(maxAgeHours);
            String pattern = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "chain:*";
            Set<String> chainKeys = redisStateManager.keys(pattern);
            
            long deletedCount = 0;
            for (String chainKey : chainKeys) {
                AttackChainAnalysis analysis = redisStateManager.getObject(chainKey, AttackChainAnalysis.class);
                if (analysis != null && analysis.getAnalysisTime() != null && 
                    analysis.getAnalysisTime().isBefore(cutoffTime)) {
                    deleteAttackChain(analysis.getAttackChainId());
                    deletedCount++;
                }
            }
            
            log.info("清理过期攻击链完成: 清理数量={}, 截止时间={}", deletedCount, cutoffTime);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("清理过期攻击链失败", e);
            return 0;
        }
    }
    
    /**
     * 获取攻击链统计信息
     * 
     * @return 统计信息
     */
    public AttackChainStatistics getStatistics() {
        try {
            // 统计总攻击链数量
            String pattern = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "chain:*";
            Set<String> chainKeys = redisStateManager.keys(pattern);
            int totalChains = chainKeys.size();
            
            // 统计活跃攻击链数量
            List<String> activeChains = getActiveAttackChains();
            int activeChainCount = activeChains.size();
            
            // 统计各阶段攻击链数量
            Map<CyberKillChainStage, Integer> stageDistribution = new HashMap<>();
            for (CyberKillChainStage stage : CyberKillChainStage.values()) {
                List<String> stageChains = getAttackChainsByStage(stage);
                stageDistribution.put(stage, stageChains.size());
            }
            
            return new AttackChainStatistics(totalChains, activeChainCount, stageDistribution);
            
        } catch (Exception e) {
            log.error("获取攻击链统计信息失败", e);
            return new AttackChainStatistics(0, 0, new HashMap<>());
        }
    }
    
    /**
     * 更新攻击链索引
     * 
     * @param analysis 攻击链分析结果
     */
    private void updateAttackChainIndexes(AttackChainAnalysis analysis) {
        String attackChainId = analysis.getAttackChainId();
        
        // 按日期索引
        if (analysis.getAnalysisTime() != null) {
            String dateKey = analysis.getAnalysisTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String dateIndexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:date:" + dateKey;
            redisStateManager.sadd(dateIndexKey, attackChainId);
            redisStateManager.expire(dateIndexKey, ATTACK_CHAIN_TTL);
        }
        
        // 按阶段索引
        if (analysis.getProgressAssessment() != null && analysis.getProgressAssessment().getCurrentStage() != null) {
            CyberKillChainStage stage = analysis.getProgressAssessment().getCurrentStage();
            String stageIndexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:stage:" + stage.getStageCode();
            redisStateManager.sadd(stageIndexKey, attackChainId);
            redisStateManager.expire(stageIndexKey, ATTACK_CHAIN_TTL);
        }
        
        // 按IP索引
        if (analysis.getEvents() != null) {
            Set<String> attackerIps = new HashSet<>();
            Set<String> victimIps = new HashSet<>();
            
            for (AttackChainEvent event : analysis.getEvents()) {
                if (event.getSourceIp() != null) {
                    attackerIps.add(event.getSourceIp());
                }
                if (event.getTargetIp() != null) {
                    victimIps.add(event.getTargetIp());
                }
            }
            
            for (String attackerIp : attackerIps) {
                String attackerIndexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:attacker_ip:" + attackerIp;
                redisStateManager.sadd(attackerIndexKey, attackChainId);
                redisStateManager.expire(attackerIndexKey, ATTACK_CHAIN_TTL);
            }
            
            for (String victimIp : victimIps) {
                String victimIndexKey = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:victim_ip:" + victimIp;
                redisStateManager.sadd(victimIndexKey, attackChainId);
                redisStateManager.expire(victimIndexKey, ATTACK_CHAIN_TTL);
            }
        }
    }
    
    /**
     * 从索引中移除攻击链
     * 
     * @param attackChainId 攻击链ID
     */
    private void removeFromIndexes(String attackChainId) {
        // 这里可以实现更精确的索引清理，但为了简化，使用通用方法
        String indexPattern = RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "index:*";
        Set<String> indexKeys = redisStateManager.keys(indexPattern);
        
        for (String indexKey : indexKeys) {
            redisStateManager.srem(indexKey, attackChainId);
        }
    }
    
    /**
     * 构建攻击链键
     * 
     * @param attackChainId 攻击链ID
     * @return 攻击链键
     */
    private String buildAttackChainKey(String attackChainId) {
        return RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "chain:" + attackChainId;
    }
    
    /**
     * 构建攻击链事件键
     * 
     * @param attackChainId 攻击链ID
     * @param eventId 事件ID
     * @return 攻击链事件键
     */
    private String buildAttackChainEventKey(String attackChainId, String eventId) {
        return RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "event:" + attackChainId + ":" + eventId;
    }
    
    /**
     * 构建攻击链事件列表键
     * 
     * @param attackChainId 攻击链ID
     * @return 攻击链事件列表键
     */
    private String buildAttackChainEventListKey(String attackChainId) {
        return RedisStateManager.KEY_PREFIX_ATTACK_CHAIN + "events:" + attackChainId;
    }
    
    /**
     * 攻击链统计信息
     */
    @lombok.Getter
    @lombok.AllArgsConstructor
    public static class AttackChainStatistics {
        private final int totalChains;
        private final int activeChains;
        private final Map<CyberKillChainStage, Integer> stageDistribution;
    }
}
