package com.geeksec.sessionthreatdetector.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 检测器类型枚举
 * 定义所有威胁检测器的类型、ID和描述信息
 * 
 * <AUTHOR>
 */
@Getter
public enum DetectorTypeEnum {
    
    // ========================== 基础检测器 ==========================
    /** SSL指纹检测器 */
    SSL_FINGERPRINT(99016, "SSL指纹检测器", "检测SSL/TLS流量中的指纹特征", "特征识别"),
    
    /** 指纹随机化检测器 */
    FINGERPRINT_RANDOMIZATION(99015, "指纹随机化检测器", "检测客户端指纹随机化行为", "行为识别"),
    
    /** 域名连接检测器 */
    DOMAIN_CONNECTION(99017, "域名连接检测器", "检测客户端与域名的连接行为", "行为识别"),
    
    // ========================== 暴力破解检测器 ==========================
    /** Web登录爆破检测器 */
    WEB_LOGIN_BRUTE_FORCE(99018, "Web登录爆破检测器", "检测Web应用登录暴力破解攻击", "行为识别"),
    
    /** RDP爆破检测器 */
    RDP_BRUTE_FORCE(99021, "RDP爆破检测器", "检测远程桌面协议暴力破解攻击", "行为识别"),
    
    /** Oracle爆破检测器 */
    ORACLE_BRUTE_FORCE(99022, "Oracle爆破检测器", "检测Oracle数据库暴力破解攻击", "行为识别"),
    
    /** MySQL爆破检测器 */
    MYSQL_BRUTE_FORCE(99023, "MySQL爆破检测器", "检测MySQL数据库暴力破解攻击", "行为识别"),
    
    /** SMB爆破检测器 */
    SMB_BRUTE_FORCE(99024, "SMB爆破检测器", "检测SMB协议暴力破解攻击", "行为识别"),
    
    // ========================== 扫描检测器 ==========================
    /** 端口扫描检测器 */
    PORT_SCAN(99019, "端口扫描检测器", "检测网络端口扫描行为", "行为识别"),
    
    /** X-Ray漏扫检测器 */
    XRAY_SCAN(99025, "X-Ray漏扫检测器", "检测X-Ray漏洞扫描工具", "工具识别"),
    
    // ========================== 隧道检测器 ==========================
    /** DNS隧道检测器 */
    DNS_TUNNEL(99013, "DNS隧道检测器", "检测DNS隐蔽隧道通信", "机器学习"),
    
    /** 挖矿连接检测器 */
    MINING_CONNECTION(99014, "挖矿连接检测器", "检测尝试挖矿连接行为", "特征识别"),
    
    /** Neoregeo隧道检测器 */
    NEOREGEO_TUNNEL(99020, "Neoregeo隧道检测器", "检测Neoregeo隧道通信", "特征识别"),
    
    /** Suo5隧道检测器 */
    SUO5_TUNNEL(99026, "Suo5隧道检测器", "检测Suo5隧道工具", "工具识别"),
    
    /** TCP隧道检测器 */
    TCP_TUNNEL(99033, "TCP隧道检测器", "检测TCP隐蔽隧道通信", "行为识别"),
    
    /** HTTP隧道检测器 */
    HTTP_TUNNEL(99034, "HTTP隧道检测器", "检测HTTP隐蔽隧道通信", "行为识别"),
    
    /** ICMP隧道检测器 */
    ICMP_TUNNEL(99036, "ICMP隧道检测器", "检测ICMP隐蔽隧道通信", "行为识别"),
    
    /** SSL隧道检测器 */
    SSL_TUNNEL(99037, "SSL隧道检测器", "检测SSL隐蔽隧道通信", "行为识别"),
    
    /** NTP隧道检测器 */
    NTP_TUNNEL(99038, "NTP隧道检测器", "检测NTP隐蔽隧道通信", "行为识别"),
    
    // ========================== WebShell检测器 ==========================
    /** WebShell检测器 */
    WEBSHELL(99031, "WebShell检测器", "检测各种WebShell攻击工具", "特征识别"),
    
    // ========================== 黑客工具检测器 ==========================
    /** 冰蝎检测器 */
    BEHINDER(99027, "冰蝎检测器", "检测冰蝎WebShell管理工具", "工具识别"),
    
    /** 蚁剑检测器 */
    ANTSWORD(99028, "蚁剑检测器", "检测蚁剑WebShell管理工具", "工具识别"),
    
    /** 加密工具检测器 */
    ENCRYPTED_TOOL(99032, "加密工具检测器", "检测特定协议攻击工具", "工具识别"),
    
    // ========================== 远程控制检测器 ==========================
    /** 标准远程控制协议C2检测器 */
    STANDARD_RC_C2(99030, "标准远程控制协议C2检测器", "检测标准远程控制协议下的C2行为", "行为识别"),
    
    /** 未知远程控制协议检测器 */
    UNKNOWN_RC_PROTOCOL(99035, "未知远程控制协议检测器", "检测未知远程控制协议", "行为识别"),
    
    /** ToDesk检测器 */
    TODESK(99039, "ToDesk检测器", "检测ToDesk远程控制工具", "工具识别"),
    
    // ========================== 其他检测器 ==========================
    /** 通用隧道检测器 */
    GENERAL_TUNNEL(99029, "通用隧道检测器", "检测通用隧道通信行为", "行为识别");
    
    /** 检测器ID */
    private final Integer detectorId;
    
    /** 检测器名称 */
    private final String detectorName;
    
    /** 检测器描述 */
    private final String description;
    
    /** 检测算法类型 */
    private final String algorithm;
    
    /**
     * 构造函数
     */
    DetectorTypeEnum(Integer detectorId, String detectorName, String description, String algorithm) {
        this.detectorId = detectorId;
        this.detectorName = detectorName;
        this.description = description;
        this.algorithm = algorithm;
    }
    
    /**
     * ID到枚举的映射
     */
    private static final Map<Integer, DetectorTypeEnum> ID_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DetectorTypeEnum::getDetectorId, Function.identity()));
    
    /**
     * 名称到枚举的映射
     */
    private static final Map<String, DetectorTypeEnum> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DetectorTypeEnum::getDetectorName, Function.identity()));
    
    /**
     * 根据检测器ID获取枚举
     * 
     * @param detectorId 检测器ID
     * @return 检测器类型枚举，如果不存在返回null
     */
    public static DetectorTypeEnum getByDetectorId(Integer detectorId) {
        return ID_MAP.get(detectorId);
    }
    
    /**
     * 根据检测器名称获取枚举
     * 
     * @param detectorName 检测器名称
     * @return 检测器类型枚举，如果不存在返回null
     */
    public static DetectorTypeEnum getByDetectorName(String detectorName) {
        return NAME_MAP.get(detectorName);
    }
    
    /**
     * 检查检测器ID是否存在
     * 
     * @param detectorId 检测器ID
     * @return 是否存在
     */
    public static boolean existsDetectorId(Integer detectorId) {
        return ID_MAP.containsKey(detectorId);
    }
    
    /**
     * 获取所有检测器ID
     * 
     * @return 检测器ID数组
     */
    public static Integer[] getAllDetectorIds() {
        return ID_MAP.keySet().toArray(new Integer[0]);
    }
    
    /**
     * 获取所有检测器名称
     * 
     * @return 检测器名称数组
     */
    public static String[] getAllDetectorNames() {
        return NAME_MAP.keySet().toArray(new String[0]);
    }
}
