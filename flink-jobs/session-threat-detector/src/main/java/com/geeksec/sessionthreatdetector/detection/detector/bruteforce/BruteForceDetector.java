package com.geeksec.sessionthreatdetector.detection.detector.bruteforce;

import com.geeksec.sessionthreatdetector.detection.DetectorType;
import com.geeksec.sessionthreatdetector.detection.ThreatDetector;
import com.geeksec.sessionthreatdetector.model.detection.DetectionResult;
import com.geeksec.sessionthreatdetector.model.input.HttpInfo;
import com.geeksec.sessionthreatdetector.model.input.NetworkEvent;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 暴力破解检测器
 * 检测各种暴力破解攻击，包括Web登录、SSH、RDP、数据库等协议的暴力破解
 *
 * <AUTHOR>
 */
@Slf4j
public class BruteForceDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 检测阈值
    private static final int LOGIN_ATTEMPT_THRESHOLD = 10; // 登录尝试次数阈值
    private static final long TIME_WINDOW_MS = 300000; // 5分钟时间窗口
    private static final int POST_REQUEST_THRESHOLD = 20; // POST请求次数阈值
    private static final int GET_REQUEST_THRESHOLD = 50; // GET请求次数阈值

    // Web登录相关的URL模式
    private static final Set<String> LOGIN_PATTERNS = new HashSet<>(Arrays.asList(
            "login", "signin", "auth", "authenticate", "logon", "signon",
            "admin", "manager", "console", "panel", "dashboard"
    ));

    // 登录相关的参数名
    private static final Set<String> LOGIN_PARAMS = new HashSet<>(Arrays.asList(
            "username", "user", "login", "email", "account", "uid",
            "password", "pass", "pwd", "passwd", "secret"
    ));

    // SSH暴力破解检测端口
    private static final Set<Integer> SSH_PORTS = new HashSet<>(Arrays.asList(22, 2222));

    // 其他协议暴力破解检测端口和阈值
    private static final Map<Integer, ProtocolConfig> PROTOCOL_CONFIGS = new HashMap<>();

    static {
        // RDP协议配置
        PROTOCOL_CONFIGS.put(3389, new ProtocolConfig("RDP", 15, 15));
        // Oracle数据库配置
        PROTOCOL_CONFIGS.put(1521, new ProtocolConfig("ORACLE", 20, 20));
        // MySQL数据库协议配置
        PROTOCOL_CONFIGS.put(3306, new ProtocolConfig("MYSQL", 25, 25));
        // SMB协议配置 - 两个端口使用相同的协议名，在统计时会合并
        PROTOCOL_CONFIGS.put(445, new ProtocolConfig("SMB", 15, 15));  // 现代SMB over TCP
        PROTOCOL_CONFIGS.put(139, new ProtocolConfig("SMB", 15, 15));  // NetBIOS over TCP（早期SMB）
    }

    // 状态存储（实际应用中应该使用Flink状态）
    private transient ConcurrentMap<String, LoginAttemptStats> loginAttempts;

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.BRUTE_FORCE;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 初始化状态存储
            if (loginAttempts == null) {
                loginAttempts = new ConcurrentHashMap<>();
            }

            // 根据事件类型进行不同的暴力破解检测
            switch (event.getEventType()) {
                case HTTP:
                    DetectionResult httpResult = detectWebBruteForce(event);
                    if (httpResult != null) {
                        results.add(httpResult);
                    }
                    break;
                case TCP:
                    DetectionResult sshResult = detectSshBruteForce(event);
                    if (sshResult != null) {
                        results.add(sshResult);
                    }

                    DetectionResult protocolResult = detectProtocolBruteForce(event);
                    if (protocolResult != null) {
                        results.add(protocolResult);
                    }
                    break;
                default:
                    // 其他类型暂不处理
                    break;
            }

        } catch (Exception e) {
            log.error("暴力破解检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测Web登录暴力破解
     */
    private DetectionResult detectWebBruteForce(NetworkEvent event) {
        HttpInfo httpInfo = event.getHttpInfo();
        if (httpInfo == null) {
            return null;
        }

        String srcIp = event.getSrcIp();
        String dstIp = event.getDstIp();
        String uri = httpInfo.getUri();
        String method = httpInfo.getMethod();

        if (srcIp == null || dstIp == null) {
            return null;
        }

        // 检查是否为登录相关请求
        if (!isLoginRelatedRequest(httpInfo)) {
            return null;
        }

        // 生成统计键
        String statsKey = srcIp + ":" + dstIp;
        
        // 获取或创建统计信息
        LoginAttemptStats stats = loginAttempts.computeIfAbsent(statsKey, 
                k -> new LoginAttemptStats());

        // 更新统计信息
        long currentTime = System.currentTimeMillis();
        stats.updateStats(currentTime, method, uri);

        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);

        // 检查是否达到暴力破解阈值
        if (stats.getLoginAttempts() >= LOGIN_ATTEMPT_THRESHOLD) {
            return createDetectionResult(event, "WEB_BRUTE_FORCE", 
                    "Web登录暴力破解",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    String.format("登录尝试次数: %d, POST请求: %d, GET请求: %d", 
                            stats.getLoginAttempts(), stats.getPostCount(), stats.getGetCount()));
        }

        // 检查POST请求频率
        if (stats.getPostCount() >= POST_REQUEST_THRESHOLD) {
            return createDetectionResult(event, "WEB_POST_FLOOD", 
                    "Web POST请求洪水攻击",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    String.format("POST请求次数: %d", stats.getPostCount()));
        }

        // 检查GET请求频率
        if (stats.getGetCount() >= GET_REQUEST_THRESHOLD) {
            return createDetectionResult(event, "WEB_GET_FLOOD", 
                    "Web GET请求洪水攻击",
                    DetectionResult.ThreatLevel.LOW, 0.5,
                    String.format("GET请求次数: %d", stats.getGetCount()));
        }

        return null;
    }

    /**
     * 检测SSH暴力破解
     */
    private DetectionResult detectSshBruteForce(NetworkEvent event) {
        Integer dstPort = event.getDstPort();
        if (dstPort == null || !SSH_PORTS.contains(dstPort)) {
            return null;
        }

        String srcIp = event.getSrcIp();
        String dstIp = event.getDstIp();

        if (srcIp == null || dstIp == null) {
            return null;
        }

        // 生成统计键
        String statsKey = srcIp + ":" + dstIp + ":ssh";
        
        // 获取或创建统计信息
        LoginAttemptStats stats = loginAttempts.computeIfAbsent(statsKey, 
                k -> new LoginAttemptStats());

        // 更新统计信息
        long currentTime = System.currentTimeMillis();
        stats.updateStats(currentTime, "TCP", "ssh");

        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);

        // 检查是否达到SSH暴力破解阈值
        if (stats.getLoginAttempts() >= LOGIN_ATTEMPT_THRESHOLD) {
            return createDetectionResult(event, "SSH_BRUTE_FORCE", 
                    "SSH暴力破解",
                    DetectionResult.ThreatLevel.HIGH, 0.8,
                    String.format("SSH连接尝试次数: %d", stats.getLoginAttempts()));
        }

        return null;
    }

    /**
     * 检测其他协议的暴力破解（RDP、数据库等）
     */
    private DetectionResult detectProtocolBruteForce(NetworkEvent event) {
        Integer dstPort = event.getDstPort();
        if (dstPort == null || !PROTOCOL_CONFIGS.containsKey(dstPort)) {
            return null;
        }

        ProtocolConfig config = PROTOCOL_CONFIGS.get(dstPort);
        String srcIp = event.getSrcIp();
        String dstIp = event.getDstIp();

        if (srcIp == null || dstIp == null) {
            return null;
        }

        // 生成统计键 - 对于SMB协议，统一使用"SMB"作为协议名，不区分端口
        String protocolKey = config.protocolName;
        if ("SMB".equals(config.protocolName)) {
            // SMB协议的两个端口（139和445）统一统计
            protocolKey = "SMB";
        }
        String statsKey = srcIp + ":" + dstIp + ":" + protocolKey;

        // 获取或创建统计信息
        LoginAttemptStats stats = loginAttempts.computeIfAbsent(statsKey,
                k -> new LoginAttemptStats());

        // 更新统计信息
        long currentTime = System.currentTimeMillis();

        // 检查连接状态，判断是否为失败连接
        boolean isFailedConnection = isConnectionFailed(event);
        stats.updateStats(currentTime, "TCP", config.protocolName, isFailedConnection);

        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);

        // 检查是否达到暴力破解阈值
        if (stats.getFailedAttempts() >= config.failThreshold ||
            stats.getLoginAttempts() >= config.requestThreshold) {

            return createDetectionResult(event, config.protocolName + "_BRUTE_FORCE",
                    config.protocolName + "暴力破解攻击",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    String.format("%s暴力破解: 连接尝试次数=%d, 失败次数=%d, 阈值=%d",
                            config.protocolName, stats.getLoginAttempts(), stats.getFailedAttempts(), config.failThreshold));
        }

        return null;
    }

    /**
     * 判断TCP连接是否失败
     */
    private boolean isConnectionFailed(NetworkEvent event) {
        if (event.getTcpInfo() == null) {
            return false;
        }

        String connectionState = event.getTcpInfo().getConnectionState();
        if (connectionState == null) {
            return false;
        }

        // 根据连接状态判断是否为失败连接
        return connectionState.contains("RESET") ||
               connectionState.contains("REFUSED") ||
               connectionState.contains("TIMEOUT") ||
               connectionState.contains("FAILED");
    }

    /**
     * 检查是否为登录相关请求
     */
    private boolean isLoginRelatedRequest(HttpInfo httpInfo) {
        String uri = httpInfo.getUri();
        String requestBody = httpInfo.getRequestBody();

        if (uri == null) {
            return false;
        }

        String lowerUri = uri.toLowerCase();

        // 检查URI中的登录模式
        for (String pattern : LOGIN_PATTERNS) {
            if (lowerUri.contains(pattern)) {
                return true;
            }
        }

        // 检查请求体中的登录参数
        if (requestBody != null) {
            String lowerBody = requestBody.toLowerCase();
            for (String param : LOGIN_PARAMS) {
                if (lowerBody.contains(param + "=")) {
                    return true;
                }
            }
        }

        // 检查Content-Type是否为表单提交
        String contentType = httpInfo.getContentType();
        if (contentType != null && contentType.contains("application/x-www-form-urlencoded")) {
            return true;
        }

        return false;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType, 
                                                String threatName, DetectionResult.ThreatLevel level, 
                                                double confidence, String description) {
        return DetectionResult.builder()
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("BRUTE_FORCE_ATTACK")
                .assetLabel("ATTACK_TARGET")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        return 40; // 中等优先级
    }

    /**
     * 登录尝试统计信息
     */
    private static class LoginAttemptStats {
        private final List<Long> timestamps = new ArrayList<>();
        private final List<Long> failedTimestamps = new ArrayList<>();
        private int postCount = 0;
        private int getCount = 0;

        public void updateStats(long timestamp, String method, String uri) {
            timestamps.add(timestamp);

            if ("POST".equalsIgnoreCase(method)) {
                postCount++;
            } else if ("GET".equalsIgnoreCase(method)) {
                getCount++;
            }
        }

        public void updateStats(long timestamp, String method, String uri, boolean isFailed) {
            timestamps.add(timestamp);

            if (isFailed) {
                failedTimestamps.add(timestamp);
            }

            if ("POST".equalsIgnoreCase(method)) {
                postCount++;
            } else if ("GET".equalsIgnoreCase(method)) {
                getCount++;
            }
        }

        public void cleanExpiredData(long currentTime, long timeWindow) {
            timestamps.removeIf(timestamp -> currentTime - timestamp > timeWindow);
            failedTimestamps.removeIf(timestamp -> currentTime - timestamp > timeWindow);

            // 重新计算计数（简化处理）
            // 实际应用中应该更精确地跟踪每个请求的时间戳和类型
        }

        public int getLoginAttempts() {
            return timestamps.size();
        }

        public int getFailedAttempts() {
            return failedTimestamps.size();
        }

        public int getPostCount() {
            return postCount;
        }

        public int getGetCount() {
            return getCount;
        }
    }

    /**
     * 协议配置类
     */
    private static class ProtocolConfig {
        final String protocolName;
        final int failThreshold;
        final int requestThreshold;

        ProtocolConfig(String protocolName, int failThreshold, int requestThreshold) {
            this.protocolName = protocolName;
            this.failThreshold = failThreshold;
            this.requestThreshold = requestThreshold;
        }
    }
}
