package com.geeksec.sessionthreatdetector.control;

import com.geeksec.sessionthreatdetector.model.enums.DetectorTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 检测器开关管理器
 * 负责管理检测器的启用/禁用状态，支持动态配置更新
 * 
 * <AUTHOR>
 */
@Slf4j
public class DetectorSwitchManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 检测器开关配置
     */
    private volatile DetectorSwitchConfig switchConfig;
    
    /**
     * 广播状态描述符
     */
    public static final MapStateDescriptor<Integer, Integer> DETECTOR_SWITCH_STATE_DESCRIPTOR = 
            new MapStateDescriptor<>(
                    "detectorSwitchState",
                    BasicTypeInfo.INT_TYPE_INFO,
                    BasicTypeInfo.INT_TYPE_INFO
            );
    
    /**
     * 单例实例
     */
    private static volatile DetectorSwitchManager instance;
    
    /**
     * 私有构造函数
     */
    private DetectorSwitchManager() {
        this.switchConfig = new DetectorSwitchConfig();
    }
    
    /**
     * 获取单例实例
     * 
     * @return 检测器开关管理器实例
     */
    public static DetectorSwitchManager getInstance() {
        if (instance == null) {
            synchronized (DetectorSwitchManager.class) {
                if (instance == null) {
                    instance = new DetectorSwitchManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化检测器开关管理器
     * 
     * @param initialConfig 初始配置
     */
    public void initialize(DetectorSwitchConfig initialConfig) {
        if (initialConfig != null) {
            this.switchConfig = initialConfig.copy();
            log.info("检测器开关管理器初始化完成");
            this.switchConfig.printSwitchStatus();
        } else {
            log.warn("初始配置为空，使用默认配置");
        }
    }
    
    /**
     * 初始化检测器开关管理器（使用默认配置）
     */
    public void initialize() {
        initialize(null);
    }
    
    /**
     * 检查检测器是否启用
     * 
     * @param detectorId 检测器ID
     * @return 是否启用
     */
    public boolean isDetectorEnabled(Integer detectorId) {
        return switchConfig.isDetectorEnabled(detectorId);
    }
    
    /**
     * 检查检测器是否启用（通过检测器类型）
     * 
     * @param detectorType 检测器类型
     * @return 是否启用
     */
    public boolean isDetectorEnabled(DetectorTypeEnum detectorType) {
        return switchConfig.isDetectorEnabled(detectorType);
    }
    
    /**
     * 检查检测器是否启用（通过检测器名称）
     * 
     * @param detectorName 检测器名称
     * @return 是否启用
     */
    public boolean isDetectorEnabled(String detectorName) {
        return switchConfig.isDetectorEnabled(detectorName);
    }
    
    /**
     * 更新检测器开关状态
     * 
     * @param switchUpdates 开关更新映射
     */
    public synchronized void updateDetectorSwitches(Map<Integer, Integer> switchUpdates) {
        if (switchUpdates == null || switchUpdates.isEmpty()) {
            log.warn("检测器开关更新为空，跳过更新");
            return;
        }
        
        log.info("更新检测器开关状态，更新数量: {}", switchUpdates.size());
        
        // 创建新的配置副本
        DetectorSwitchConfig newConfig = switchConfig.copy();
        newConfig.updateDetectorSwitches(switchUpdates);
        
        // 原子性更新配置
        this.switchConfig = newConfig;
        
        log.info("检测器开关状态更新完成");
        if (log.isDebugEnabled()) {
            this.switchConfig.printSwitchStatus();
        }
    }
    
    /**
     * 从广播状态更新检测器开关
     * 
     * @param broadcastState 广播状态
     */
    public void updateFromBroadcastState(BroadcastState<Integer, Integer> broadcastState) {
        try {
            Map<Integer, Integer> switchUpdates = new ConcurrentHashMap<>();
            
            // 从广播状态读取所有开关配置
            for (Map.Entry<Integer, Integer> entry : broadcastState.entries()) {
                switchUpdates.put(entry.getKey(), entry.getValue());
            }
            
            if (!switchUpdates.isEmpty()) {
                updateDetectorSwitches(switchUpdates);
            }
            
        } catch (Exception e) {
            log.error("从广播状态更新检测器开关失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 将当前配置同步到广播状态
     * 
     * @param broadcastState 广播状态
     */
    public void syncToBroadcastState(BroadcastState<Integer, Integer> broadcastState) {
        try {
            // 清空现有状态
            broadcastState.clear();
            
            // 同步当前配置到广播状态
            Map<Integer, Integer> currentSwitches = switchConfig.getDetectorSwitches();
            for (Map.Entry<Integer, Integer> entry : currentSwitches.entrySet()) {
                broadcastState.put(entry.getKey(), entry.getValue());
            }
            
            log.info("检测器开关配置已同步到广播状态，同步数量: {}", currentSwitches.size());
            
        } catch (Exception e) {
            log.error("同步检测器开关配置到广播状态失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 启用指定检测器
     * 
     * @param detectorId 检测器ID
     */
    public synchronized void enableDetector(Integer detectorId) {
        DetectorSwitchConfig newConfig = switchConfig.copy();
        newConfig.setDetectorSwitch(detectorId, true);
        this.switchConfig = newConfig;
    }
    
    /**
     * 禁用指定检测器
     * 
     * @param detectorId 检测器ID
     */
    public synchronized void disableDetector(Integer detectorId) {
        DetectorSwitchConfig newConfig = switchConfig.copy();
        newConfig.setDetectorSwitch(detectorId, false);
        this.switchConfig = newConfig;
    }
    
    /**
     * 启用所有检测器
     */
    public synchronized void enableAllDetectors() {
        DetectorSwitchConfig newConfig = switchConfig.copy();
        newConfig.enableAllDetectors();
        this.switchConfig = newConfig;
    }
    
    /**
     * 禁用所有检测器
     */
    public synchronized void disableAllDetectors() {
        DetectorSwitchConfig newConfig = switchConfig.copy();
        newConfig.disableAllDetectors();
        this.switchConfig = newConfig;
    }
    
    /**
     * 获取当前配置
     * 
     * @return 当前检测器开关配置
     */
    public DetectorSwitchConfig getCurrentConfig() {
        return switchConfig.copy();
    }
    
    /**
     * 获取开关状态摘要
     * 
     * @return 开关状态摘要
     */
    public Map<String, Object> getSwitchSummary() {
        return switchConfig.getSwitchSummary();
    }
    
    /**
     * 打印当前开关状态
     */
    public void printCurrentStatus() {
        log.info("=== 检测器开关管理器状态 ===");
        switchConfig.printSwitchStatus();
        log.info("=== 状态打印完成 ===");
    }
    
    /**
     * 重置为默认配置
     */
    public synchronized void resetToDefault() {
        log.info("重置检测器开关配置为默认状态");
        this.switchConfig = new DetectorSwitchConfig();
        this.switchConfig.printSwitchStatus();
    }
    
    /**
     * 验证检测器ID是否有效
     * 
     * @param detectorId 检测器ID
     * @return 是否有效
     */
    public boolean isValidDetectorId(Integer detectorId) {
        return DetectorTypeEnum.existsDetectorId(detectorId);
    }
    
    /**
     * 获取所有有效的检测器ID
     * 
     * @return 检测器ID数组
     */
    public Integer[] getAllValidDetectorIds() {
        return DetectorTypeEnum.getAllDetectorIds();
    }
    
    /**
     * 获取检测器统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        int total = switchConfig.getDetectorSwitches().size();
        int enabled = switchConfig.getEnabledDetectorCount();
        int disabled = switchConfig.getDisabledDetectorCount();
        
        return String.format("检测器统计: 总数=%d, 启用=%d, 禁用=%d", total, enabled, disabled);
    }
}
