package com.geeksec.sessionthreatdetector.subscription.function;

import com.geeksec.sessionthreatdetector.model.output.Alarm;
import com.geeksec.sessionthreatdetector.subscription.manager.SubscriptionManager;
import com.geeksec.sessionthreatdetector.subscription.notification.NotificationSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 告警订阅Flink函数
 * 处理告警流中的订阅匹配和通知发送
 * 
 * <AUTHOR>
 */
@Slf4j
public class AlarmSubscriptionFunction extends RichFlatMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅管理器
     */
    private transient SubscriptionManager subscriptionManager;
    
    /**
     * 定时任务执行器
     */
    private transient ScheduledExecutorService scheduledExecutor;
    
    /**
     * 是否启用订阅处理
     */
    private final boolean subscriptionEnabled;
    
    /**
     * 是否透传原始告警
     */
    private final boolean passthroughAlarms;
    
    /**
     * 统计信息输出间隔（秒）
     */
    private final int statisticsIntervalSeconds;
    
    /**
     * 订阅配置刷新间隔（秒）
     */
    private final int configRefreshIntervalSeconds;
    
    /**
     * 统计信息
     */
    private transient long processedCount = 0;
    private transient long matchedCount = 0;
    private transient long notifiedCount = 0;
    private transient long failedCount = 0;
    private transient long lastLogTime = 0;
    
    /**
     * 构造函数
     * 
     * @param subscriptionEnabled 是否启用订阅处理
     * @param passthroughAlarms 是否透传原始告警
     * @param statisticsIntervalSeconds 统计信息输出间隔
     * @param configRefreshIntervalSeconds 配置刷新间隔
     */
    public AlarmSubscriptionFunction(boolean subscriptionEnabled,
                                   boolean passthroughAlarms,
                                   int statisticsIntervalSeconds,
                                   int configRefreshIntervalSeconds) {
        this.subscriptionEnabled = subscriptionEnabled;
        this.passthroughAlarms = passthroughAlarms;
        this.statisticsIntervalSeconds = statisticsIntervalSeconds;
        this.configRefreshIntervalSeconds = configRefreshIntervalSeconds;
    }
    
    /**
     * 默认构造函数
     */
    public AlarmSubscriptionFunction() {
        this(true, true, 60, 300); // 默认启用订阅，透传告警，60秒统计间隔，300秒配置刷新
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        if (!subscriptionEnabled) {
            log.info("告警订阅功能已禁用");
            return;
        }
        
        // 初始化通知发送器
        NotificationSender notificationSender = new NotificationSender();
        
        // 初始化订阅管理器
        subscriptionManager = new SubscriptionManager(notificationSender);
        
        // 加载初始订阅配置
        loadSubscriptionConfig();
        
        // 启动定时任务
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "AlarmSubscription-Timer");
            t.setDaemon(true);
            return t;
        });
        
        // 定时输出统计信息
        if (statisticsIntervalSeconds > 0) {
            scheduledExecutor.scheduleAtFixedRate(
                    this::printStatistics,
                    statisticsIntervalSeconds,
                    statisticsIntervalSeconds,
                    TimeUnit.SECONDS
            );
        }
        
        // 定时刷新配置
        if (configRefreshIntervalSeconds > 0) {
            scheduledExecutor.scheduleAtFixedRate(
                    this::refreshSubscriptionConfig,
                    configRefreshIntervalSeconds,
                    configRefreshIntervalSeconds,
                    TimeUnit.SECONDS
            );
        }
        
        log.info("告警订阅函数初始化完成，订阅处理: {}, 透传告警: {}", 
                subscriptionEnabled, passthroughAlarms);
    }
    
    @Override
    public void flatMap(Alarm alarm, Collector<Alarm> out) throws Exception {
        if (alarm == null) {
            return;
        }
        
        processedCount++;
        
        // 如果启用透传，直接输出原始告警
        if (passthroughAlarms) {
            out.collect(alarm);
        }
        
        // 如果启用订阅处理，进行订阅匹配和通知
        if (subscriptionEnabled && subscriptionManager != null) {
            try {
                SubscriptionManager.SubscriptionProcessResult result = 
                        subscriptionManager.processAlarm(alarm);
                
                if (result.isSuccess()) {
                    if (result.getMatchedCount() > 0) {
                        matchedCount++;
                        
                        // 统计通知结果
                        long successNotifications = result.getNotificationResults().stream()
                                .mapToLong(nr -> nr.isSuccess() ? 1 : 0)
                                .sum();
                        
                        long failedNotifications = result.getNotificationResults().stream()
                                .mapToLong(nr -> nr.isSuccess() ? 0 : 1)
                                .sum();
                        
                        notifiedCount += successNotifications;
                        failedCount += failedNotifications;
                        
                        log.debug("告警订阅处理完成: {}, 匹配订阅: {}, 成功通知: {}, 失败通知: {}", 
                                alarm.getAlarmId(), result.getMatchedCount(), 
                                successNotifications, failedNotifications);
                    }
                } else {
                    failedCount++;
                    log.warn("告警订阅处理失败: {}, 错误: {}", 
                            alarm.getAlarmId(), result.getMessage());
                }
                
            } catch (Exception e) {
                failedCount++;
                log.error("处理告警订阅时发生异常: {}", e.getMessage(), e);
            }
        }
    }
    
    @Override
    public void close() throws Exception {
        super.close();
        
        log.info("关闭告警订阅函数");
        
        // 关闭定时任务
        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 打印最终统计信息
        printFinalStatistics();
    }
    
    /**
     * 加载订阅配置
     */
    private void loadSubscriptionConfig() {
        if (subscriptionManager == null) {
            return;
        }
        
        try {
            // 这里应该从配置文件、数据库或配置中心加载订阅配置
            // 为了演示，我们创建一些示例订阅
            createSampleSubscriptions();
            
            log.info("订阅配置加载完成");
            
        } catch (Exception e) {
            log.error("加载订阅配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 刷新订阅配置
     */
    private void refreshSubscriptionConfig() {
        try {
            // 这里应该重新加载最新的订阅配置
            log.debug("刷新订阅配置");
            
            // 可以添加配置变更检测逻辑
            
        } catch (Exception e) {
            log.error("刷新订阅配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 创建示例订阅
     */
    private void createSampleSubscriptions() {
        // 这里创建一些示例订阅用于演示
        // 实际使用时应该从配置源加载
        
        try {
            // 示例1：高危告警邮件通知
            com.geeksec.threatdetector.subscription.model.AlarmSubscription highRiskSubscription = 
                    com.geeksec.threatdetector.subscription.model.AlarmSubscription.builder()
                            .subscriptionId("high-risk-email")
                            .userId("admin")
                            .username("管理员")
                            .subscriptionName("高危告警邮件通知")
                            .description("当出现高危告警时发送邮件通知")
                            .enabled(true)
                            .priority(com.geeksec.threatdetector.subscription.model.AlarmSubscription.SubscriptionPriority.HIGH)
                            .createTime(java.time.LocalDateTime.now())
                            .build();
            
            // 添加规则：告警类型为高危
            com.geeksec.threatdetector.subscription.model.SubscriptionRule highRiskRule = 
                    com.geeksec.threatdetector.subscription.model.SubscriptionRule.createFieldRule(
                            "alarmType", 
                            com.geeksec.threatdetector.subscription.model.SubscriptionRule.Operator.CONTAINS, 
                            "高危");
            highRiskSubscription.addRule(highRiskRule);
            
            // 添加邮件通知渠道
            com.geeksec.threatdetector.subscription.model.NotificationChannel emailChannel = 
                    com.geeksec.threatdetector.subscription.model.NotificationChannel.createEmailChannel(
                            "<EMAIL>", "default_email");
            highRiskSubscription.addChannel(emailChannel);
            
            subscriptionManager.addSubscription(highRiskSubscription);
            
            // 示例2：恶意软件告警短信通知
            com.geeksec.threatdetector.subscription.model.AlarmSubscription malwareSubscription = 
                    com.geeksec.threatdetector.subscription.model.AlarmSubscription.builder()
                            .subscriptionId("malware-sms")
                            .userId("security-team")
                            .username("安全团队")
                            .subscriptionName("恶意软件告警短信通知")
                            .description("当检测到恶意软件时发送短信通知")
                            .enabled(true)
                            .priority(com.geeksec.threatdetector.subscription.model.AlarmSubscription.SubscriptionPriority.URGENT)
                            .createTime(java.time.LocalDateTime.now())
                            .build();
            
            // 添加规则：威胁类型包含恶意软件
            com.geeksec.threatdetector.subscription.model.SubscriptionRule malwareRule = 
                    com.geeksec.threatdetector.subscription.model.SubscriptionRule.createFieldRule(
                            "threatType", 
                            com.geeksec.threatdetector.subscription.model.SubscriptionRule.Operator.CONTAINS, 
                            "恶意软件");
            malwareSubscription.addRule(malwareRule);
            
            // 添加短信通知渠道
            com.geeksec.threatdetector.subscription.model.NotificationChannel smsChannel = 
                    com.geeksec.threatdetector.subscription.model.NotificationChannel.createSmsChannel(
                            "13800138000", "default_sms");
            malwareSubscription.addChannel(smsChannel);
            
            subscriptionManager.addSubscription(malwareSubscription);
            
            log.info("创建示例订阅完成");
            
        } catch (Exception e) {
            log.error("创建示例订阅失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 定时打印统计信息
     */
    private void printStatistics() {
        try {
            long currentTime = System.currentTimeMillis();
            
            log.info("=== 告警订阅统计信息 ===");
            log.info("处理告警数: {}, 匹配订阅数: {}, 成功通知数: {}, 失败通知数: {}", 
                    processedCount, matchedCount, notifiedCount, failedCount);
            
            if (processedCount > 0) {
                double matchRate = (double) matchedCount / processedCount * 100;
                log.info("订阅匹配率: {:.2f}%", matchRate);
            }
            
            if (matchedCount > 0) {
                double notificationSuccessRate = (double) notifiedCount / (notifiedCount + failedCount) * 100;
                log.info("通知成功率: {:.2f}%", notificationSuccessRate);
            }
            
            // 订阅管理器统计信息
            if (subscriptionManager != null) {
                SubscriptionManager.SubscriptionStatistics stats = subscriptionManager.getStatistics();
                log.info("订阅管理器统计: 订阅数={}, 用户数={}, 模板数={}, 总处理={}, 总匹配={}", 
                        stats.getTotalSubscriptions(), stats.getTotalUsers(), stats.getTotalTemplates(),
                        stats.getTotalProcessed(), stats.getTotalMatched());
            }
            
            log.info("=== 统计信息完成 ===");
            
        } catch (Exception e) {
            log.error("打印统计信息失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 打印最终统计信息
     */
    private void printFinalStatistics() {
        log.info("=== 告警订阅最终统计 ===");
        log.info("总处理告警数: {}", processedCount);
        log.info("匹配订阅数: {}", matchedCount);
        log.info("成功通知数: {}", notifiedCount);
        log.info("失败通知数: {}", failedCount);
        
        if (processedCount > 0) {
            double matchRate = (double) matchedCount / processedCount * 100;
            log.info("订阅匹配率: {:.2f}%", matchRate);
        }
        
        if (matchedCount > 0) {
            double notificationSuccessRate = (double) notifiedCount / (notifiedCount + failedCount) * 100;
            log.info("通知成功率: {:.2f}%", notificationSuccessRate);
        }
        
        if (subscriptionManager != null) {
            SubscriptionManager.SubscriptionStatistics stats = subscriptionManager.getStatistics();
            log.info("订阅管理器最终统计:");
            log.info("  总订阅数: {}", stats.getTotalSubscriptions());
            log.info("  总用户数: {}", stats.getTotalUsers());
            log.info("  总模板数: {}", stats.getTotalTemplates());
            log.info("  总处理数: {}", stats.getTotalProcessed());
            log.info("  总匹配数: {}", stats.getTotalMatched());
            log.info("  总通知数: {}", stats.getTotalNotified());
            log.info("  总失败数: {}", stats.getTotalFailed());
            log.info("  匹配率: {:.2f}%", stats.getMatchRate() * 100);
            log.info("  通知成功率: {:.2f}%", stats.getNotificationSuccessRate() * 100);
        }
        
        log.info("=== 最终统计完成 ===");
    }
}
