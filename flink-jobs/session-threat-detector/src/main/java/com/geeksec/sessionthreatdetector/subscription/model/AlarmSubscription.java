package com.geeksec.sessionthreatdetector.subscription.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 告警订阅模型
 * 定义用户的告警订阅配置
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmSubscription implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 订阅名称
     */
    private String subscriptionName;
    
    /**
     * 订阅描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 订阅规则列表
     */
    private List<SubscriptionRule> rules;
    
    /**
     * 通知渠道列表
     */
    private List<NotificationChannel> channels;
    
    /**
     * 通知频率控制
     */
    private NotificationFrequency frequency;
    
    /**
     * 免打扰时间设置
     */
    private QuietHours quietHours;
    
    /**
     * 订阅优先级
     */
    private SubscriptionPriority priority;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggeredTime;
    
    /**
     * 触发次数
     */
    private Long triggerCount;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 订阅优先级枚举
     */
    public enum SubscriptionPriority {
        /** 低优先级 */
        LOW(1),
        /** 普通优先级 */
        NORMAL(2),
        /** 高优先级 */
        HIGH(3),
        /** 紧急优先级 */
        URGENT(4);
        
        private final int level;
        
        SubscriptionPriority(int level) {
            this.level = level;
        }
        
        public int getLevel() {
            return level;
        }
    }
    
    /**
     * 通知频率控制
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationFrequency implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 频率类型
         */
        private FrequencyType type;
        
        /**
         * 时间间隔（分钟）
         */
        private Integer intervalMinutes;
        
        /**
         * 最大通知次数（每天）
         */
        private Integer maxNotificationsPerDay;
        
        /**
         * 批量通知阈值
         */
        private Integer batchThreshold;
        
        /**
         * 批量等待时间（分钟）
         */
        private Integer batchWaitMinutes;
        
        /**
         * 频率类型枚举
         */
        public enum FrequencyType {
            /** 实时通知 */
            REAL_TIME,
            /** 间隔通知 */
            INTERVAL,
            /** 批量通知 */
            BATCH,
            /** 摘要通知 */
            DIGEST
        }
    }
    
    /**
     * 免打扰时间设置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuietHours implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 是否启用免打扰
         */
        private Boolean enabled;
        
        /**
         * 开始时间
         */
        private LocalTime startTime;
        
        /**
         * 结束时间
         */
        private LocalTime endTime;
        
        /**
         * 免打扰日期（周几）
         */
        private List<Integer> quietDays;
        
        /**
         * 紧急告警是否忽略免打扰
         */
        private Boolean urgentIgnoreQuiet;
        
        /**
         * 时区
         */
        private String timezone;
    }
    
    /**
     * 检查当前是否在免打扰时间
     * 
     * @return 是否在免打扰时间
     */
    public boolean isInQuietHours() {
        if (quietHours == null || !Boolean.TRUE.equals(quietHours.getEnabled())) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalTime currentTime = now.toLocalTime();
        int currentDay = now.getDayOfWeek().getValue(); // 1=Monday, 7=Sunday
        
        // 检查是否在免打扰日期
        if (quietHours.getQuietDays() != null && 
            quietHours.getQuietDays().contains(currentDay)) {
            return true;
        }
        
        // 检查是否在免打扰时间段
        LocalTime startTime = quietHours.getStartTime();
        LocalTime endTime = quietHours.getEndTime();
        
        if (startTime != null && endTime != null) {
            if (startTime.isBefore(endTime)) {
                // 同一天内的时间段
                return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
            } else {
                // 跨天的时间段
                return !currentTime.isBefore(startTime) || !currentTime.isAfter(endTime);
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否应该发送通知
     * 
     * @param isUrgent 是否为紧急告警
     * @return 是否应该发送通知
     */
    public boolean shouldSendNotification(boolean isUrgent) {
        // 检查订阅是否启用
        if (!Boolean.TRUE.equals(enabled)) {
            return false;
        }
        
        // 检查免打扰时间
        if (isInQuietHours()) {
            // 如果是紧急告警且设置了忽略免打扰，则发送
            if (isUrgent && quietHours != null && 
                Boolean.TRUE.equals(quietHours.getUrgentIgnoreQuiet())) {
                return true;
            }
            return false;
        }
        
        // 检查通知频率限制
        return checkFrequencyLimit();
    }
    
    /**
     * 检查通知频率限制
     * 
     * @return 是否在频率限制内
     */
    private boolean checkFrequencyLimit() {
        if (frequency == null) {
            return true;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 检查间隔限制
        if (frequency.getIntervalMinutes() != null && lastTriggeredTime != null) {
            long minutesSinceLastTrigger = java.time.Duration.between(lastTriggeredTime, now).toMinutes();
            if (minutesSinceLastTrigger < frequency.getIntervalMinutes()) {
                return false;
            }
        }
        
        // 检查每日最大通知次数（这里简化处理，实际应该查询数据库）
        if (frequency.getMaxNotificationsPerDay() != null && triggerCount != null) {
            // 简化判断，实际应该统计当天的通知次数
            if (triggerCount >= frequency.getMaxNotificationsPerDay()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 更新触发信息
     */
    public void updateTriggerInfo() {
        this.lastTriggeredTime = LocalDateTime.now();
        this.triggerCount = (this.triggerCount == null ? 0 : this.triggerCount) + 1;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 检查订阅是否匹配告警
     * 
     * @param alarm 告警对象
     * @return 是否匹配
     */
    public boolean matches(Object alarm) {
        if (rules == null || rules.isEmpty()) {
            return false;
        }
        
        // 所有规则都必须匹配（AND逻辑）
        for (SubscriptionRule rule : rules) {
            if (!rule.matches(alarm)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取有效的通知渠道
     * 
     * @return 有效的通知渠道列表
     */
    public List<NotificationChannel> getActiveChannels() {
        if (channels == null) {
            return java.util.Collections.emptyList();
        }
        
        return channels.stream()
                .filter(channel -> Boolean.TRUE.equals(channel.getEnabled()))
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 添加订阅规则
     * 
     * @param rule 订阅规则
     */
    public void addRule(SubscriptionRule rule) {
        if (rules == null) {
            rules = new java.util.ArrayList<>();
        }
        rules.add(rule);
    }
    
    /**
     * 添加通知渠道
     * 
     * @param channel 通知渠道
     */
    public void addChannel(NotificationChannel channel) {
        if (channels == null) {
            channels = new java.util.ArrayList<>();
        }
        channels.add(channel);
    }
    
    /**
     * 检查订阅是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return enabled != null && enabled &&
               rules != null && !rules.isEmpty() &&
               channels != null && !channels.isEmpty() &&
               userId != null && !userId.trim().isEmpty();
    }
}
