package com.geeksec.sessionthreatdetector.subscription.manager;

import com.geeksec.sessionthreatdetector.subscription.model.AlarmSubscription;
import com.geeksec.sessionthreatdetector.subscription.model.NotificationChannel;
import com.geeksec.sessionthreatdetector.subscription.model.NotificationTemplate;
import com.geeksec.sessionthreatdetector.subscription.notification.NotificationSender;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订阅管理器
 * 管理告警订阅的核心组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class SubscriptionManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅缓存
     */
    private final ConcurrentMap<String, AlarmSubscription> subscriptions;
    
    /**
     * 用户订阅索引
     */
    private final ConcurrentMap<String, List<String>> userSubscriptionIndex;
    
    /**
     * 模板缓存
     */
    private final ConcurrentMap<String, NotificationTemplate> templates;
    
    /**
     * 通知发送器
     */
    private final NotificationSender notificationSender;
    
    /**
     * 统计信息
     */
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalMatched = new AtomicLong(0);
    private final AtomicLong totalNotified = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    
    /**
     * 最后清理时间
     */
    private volatile LocalDateTime lastCleanupTime;
    
    /**
     * 构造函数
     * 
     * @param notificationSender 通知发送器
     */
    public SubscriptionManager(NotificationSender notificationSender) {
        this.subscriptions = new ConcurrentHashMap<>();
        this.userSubscriptionIndex = new ConcurrentHashMap<>();
        this.templates = new ConcurrentHashMap<>();
        this.notificationSender = notificationSender;
        this.lastCleanupTime = LocalDateTime.now();
        
        // 初始化默认模板
        initializeDefaultTemplates();
        
        log.info("订阅管理器初始化完成");
    }
    
    /**
     * 处理告警
     * 
     * @param alarm 告警对象
     * @return 处理结果
     */
    public SubscriptionProcessResult processAlarm(Object alarm) {
        if (alarm == null) {
            return SubscriptionProcessResult.error("告警对象为空");
        }
        
        totalProcessed.incrementAndGet();
        
        try {
            List<AlarmSubscription> matchedSubscriptions = findMatchingSubscriptions(alarm);
            
            if (matchedSubscriptions.isEmpty()) {
                return SubscriptionProcessResult.noMatch("未找到匹配的订阅");
            }
            
            totalMatched.incrementAndGet();
            
            List<NotificationResult> notificationResults = new ArrayList<>();
            
            for (AlarmSubscription subscription : matchedSubscriptions) {
                try {
                    NotificationResult result = sendNotification(subscription, alarm);
                    notificationResults.add(result);
                    
                    if (result.isSuccess()) {
                        totalNotified.incrementAndGet();
                        subscription.updateTriggerInfo();
                    } else {
                        totalFailed.incrementAndGet();
                    }
                    
                } catch (Exception e) {
                    log.error("发送通知失败，订阅ID: {}, 错误: {}", 
                            subscription.getSubscriptionId(), e.getMessage(), e);
                    totalFailed.incrementAndGet();
                    
                    notificationResults.add(NotificationResult.failure(
                            subscription.getSubscriptionId(), e.getMessage()));
                }
            }
            
            return SubscriptionProcessResult.success(matchedSubscriptions.size(), notificationResults);
            
        } catch (Exception e) {
            log.error("处理告警订阅失败: {}", e.getMessage(), e);
            return SubscriptionProcessResult.error("处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 查找匹配的订阅
     * 
     * @param alarm 告警对象
     * @return 匹配的订阅列表
     */
    private List<AlarmSubscription> findMatchingSubscriptions(Object alarm) {
        List<AlarmSubscription> matched = new ArrayList<>();
        
        for (AlarmSubscription subscription : subscriptions.values()) {
            if (subscription.isValid() && subscription.matches(alarm)) {
                // 检查是否应该发送通知（考虑免打扰时间等）
                if (subscription.shouldSendNotification(isUrgentAlarm(alarm))) {
                    matched.add(subscription);
                }
            }
        }
        
        // 按优先级排序
        matched.sort((s1, s2) -> {
            int priority1 = s1.getPriority() != null ? s1.getPriority().getLevel() : 0;
            int priority2 = s2.getPriority() != null ? s2.getPriority().getLevel() : 0;
            return Integer.compare(priority2, priority1); // 高优先级在前
        });
        
        return matched;
    }
    
    /**
     * 判断是否为紧急告警
     * 
     * @param alarm 告警对象
     * @return 是否紧急
     */
    private boolean isUrgentAlarm(Object alarm) {
        // 简单实现，可以根据实际需求扩展
        try {
            // 尝试获取告警的严重程度或优先级
            java.lang.reflect.Method getSeverity = alarm.getClass().getMethod("getSeverity");
            Object severity = getSeverity.invoke(alarm);
            return "CRITICAL".equals(severity) || "HIGH".equals(severity);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 发送通知
     * 
     * @param subscription 订阅信息
     * @param alarm 告警对象
     * @return 通知结果
     */
    private NotificationResult sendNotification(AlarmSubscription subscription, Object alarm) {
        List<NotificationChannel> channels = subscription.getActiveChannels();
        if (channels.isEmpty()) {
            return NotificationResult.failure(subscription.getSubscriptionId(), "没有可用的通知渠道");
        }
        
        List<String> successChannels = new ArrayList<>();
        List<String> failedChannels = new ArrayList<>();
        
        for (NotificationChannel channel : channels) {
            try {
                NotificationTemplate template = getTemplate(channel.getTemplateId(), channel.getChannelType());
                if (template == null) {
                    log.warn("未找到模板: {}", channel.getTemplateId());
                    failedChannels.add(channel.getChannelId());
                    continue;
                }
                
                // 准备模板上下文
                Map<String, Object> context = prepareTemplateContext(alarm, subscription);
                
                // 渲染模板
                NotificationTemplate.RenderedTemplate renderedTemplate = template.render(context);
                
                // 发送通知
                boolean sent = notificationSender.sendNotification(channel, renderedTemplate);
                
                if (sent) {
                    successChannels.add(channel.getChannelId());
                    channel.updateSendStatistics(true);
                } else {
                    failedChannels.add(channel.getChannelId());
                    channel.updateSendStatistics(false);
                }
                
            } catch (Exception e) {
                log.error("通过渠道 {} 发送通知失败: {}", channel.getChannelId(), e.getMessage(), e);
                failedChannels.add(channel.getChannelId());
                channel.updateSendStatistics(false);
            }
        }
        
        boolean success = !successChannels.isEmpty();
        String message = String.format("成功: %d, 失败: %d", successChannels.size(), failedChannels.size());
        
        return new NotificationResult(subscription.getSubscriptionId(), success, message, 
                successChannels, failedChannels);
    }
    
    /**
     * 准备模板上下文
     * 
     * @param alarm 告警对象
     * @param subscription 订阅信息
     * @return 模板上下文
     */
    private Map<String, Object> prepareTemplateContext(Object alarm, AlarmSubscription subscription) {
        Map<String, Object> context = new ConcurrentHashMap<>();
        context.put("alarm", alarm);
        context.put("subscription", subscription);
        context.put("currentTime", LocalDateTime.now());
        
        // 添加用户信息
        context.put("username", subscription.getUsername());
        context.put("userId", subscription.getUserId());
        
        return context;
    }
    
    /**
     * 获取模板
     * 
     * @param templateId 模板ID
     * @param channelType 渠道类型
     * @return 通知模板
     */
    private NotificationTemplate getTemplate(String templateId, NotificationChannel.ChannelType channelType) {
        if (templateId != null) {
            NotificationTemplate template = templates.get(templateId);
            if (template != null) {
                return template;
            }
        }
        
        // 使用默认模板
        String defaultTemplateId = "default_" + channelType.getCode();
        return templates.get(defaultTemplateId);
    }
    
    /**
     * 添加订阅
     * 
     * @param subscription 订阅信息
     */
    public void addSubscription(AlarmSubscription subscription) {
        if (subscription == null || !subscription.isValid()) {
            throw new IllegalArgumentException("无效的订阅信息");
        }
        
        subscriptions.put(subscription.getSubscriptionId(), subscription);
        
        // 更新用户订阅索引
        String userId = subscription.getUserId();
        userSubscriptionIndex.computeIfAbsent(userId, k -> new ArrayList<>())
                .add(subscription.getSubscriptionId());
        
        log.info("添加订阅: {}, 用户: {}", subscription.getSubscriptionId(), userId);
    }
    
    /**
     * 移除订阅
     * 
     * @param subscriptionId 订阅ID
     * @return 是否移除成功
     */
    public boolean removeSubscription(String subscriptionId) {
        AlarmSubscription subscription = subscriptions.remove(subscriptionId);
        if (subscription != null) {
            // 更新用户订阅索引
            String userId = subscription.getUserId();
            List<String> userSubscriptions = userSubscriptionIndex.get(userId);
            if (userSubscriptions != null) {
                userSubscriptions.remove(subscriptionId);
                if (userSubscriptions.isEmpty()) {
                    userSubscriptionIndex.remove(userId);
                }
            }
            
            log.info("移除订阅: {}, 用户: {}", subscriptionId, userId);
            return true;
        }
        return false;
    }
    
    /**
     * 获取用户的所有订阅
     * 
     * @param userId 用户ID
     * @return 订阅列表
     */
    public List<AlarmSubscription> getUserSubscriptions(String userId) {
        List<String> subscriptionIds = userSubscriptionIndex.get(userId);
        if (subscriptionIds == null || subscriptionIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<AlarmSubscription> userSubscriptions = new ArrayList<>();
        for (String subscriptionId : subscriptionIds) {
            AlarmSubscription subscription = subscriptions.get(subscriptionId);
            if (subscription != null) {
                userSubscriptions.add(subscription);
            }
        }
        
        return userSubscriptions;
    }
    
    /**
     * 添加模板
     * 
     * @param template 通知模板
     */
    public void addTemplate(NotificationTemplate template) {
        if (template == null || !template.isValid()) {
            throw new IllegalArgumentException("无效的模板信息");
        }
        
        templates.put(template.getTemplateId(), template);
        log.info("添加模板: {}", template.getTemplateId());
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    public SubscriptionStatistics getStatistics() {
        return new SubscriptionStatistics(
                subscriptions.size(),
                userSubscriptionIndex.size(),
                templates.size(),
                totalProcessed.get(),
                totalMatched.get(),
                totalNotified.get(),
                totalFailed.get(),
                lastCleanupTime
        );
    }
    
    /**
     * 初始化默认模板
     */
    private void initializeDefaultTemplates() {
        addTemplate(NotificationTemplate.createDefaultEmailTemplate());
        addTemplate(NotificationTemplate.createDefaultSmsTemplate());
        addTemplate(NotificationTemplate.createDefaultDingTalkTemplate());
        
        // 添加批量模板
        for (NotificationChannel.ChannelType channelType : NotificationChannel.ChannelType.values()) {
            addTemplate(NotificationTemplate.createBatchTemplate(channelType));
        }
        
        log.info("默认模板初始化完成，共 {} 个模板", templates.size());
    }
    
    /**
     * 订阅处理结果
     */
    public static class SubscriptionProcessResult implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final boolean success;
        private final String message;
        private final int matchedCount;
        private final List<NotificationResult> notificationResults;
        
        private SubscriptionProcessResult(boolean success, String message, int matchedCount, 
                                        List<NotificationResult> notificationResults) {
            this.success = success;
            this.message = message;
            this.matchedCount = matchedCount;
            this.notificationResults = notificationResults;
        }
        
        public static SubscriptionProcessResult success(int matchedCount, List<NotificationResult> results) {
            return new SubscriptionProcessResult(true, "处理成功", matchedCount, results);
        }
        
        public static SubscriptionProcessResult noMatch(String message) {
            return new SubscriptionProcessResult(true, message, 0, new ArrayList<>());
        }
        
        public static SubscriptionProcessResult error(String message) {
            return new SubscriptionProcessResult(false, message, 0, new ArrayList<>());
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public int getMatchedCount() { return matchedCount; }
        public List<NotificationResult> getNotificationResults() { return notificationResults; }
    }
    
    /**
     * 通知结果
     */
    public static class NotificationResult implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final String subscriptionId;
        private final boolean success;
        private final String message;
        private final List<String> successChannels;
        private final List<String> failedChannels;
        
        public NotificationResult(String subscriptionId, boolean success, String message,
                                List<String> successChannels, List<String> failedChannels) {
            this.subscriptionId = subscriptionId;
            this.success = success;
            this.message = message;
            this.successChannels = successChannels != null ? successChannels : new ArrayList<>();
            this.failedChannels = failedChannels != null ? failedChannels : new ArrayList<>();
        }
        
        public static NotificationResult failure(String subscriptionId, String message) {
            return new NotificationResult(subscriptionId, false, message, null, null);
        }
        
        // Getters
        public String getSubscriptionId() { return subscriptionId; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public List<String> getSuccessChannels() { return successChannels; }
        public List<String> getFailedChannels() { return failedChannels; }
    }
    
    /**
     * 订阅统计信息
     */
    public static class SubscriptionStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final int totalSubscriptions;
        private final int totalUsers;
        private final int totalTemplates;
        private final long totalProcessed;
        private final long totalMatched;
        private final long totalNotified;
        private final long totalFailed;
        private final LocalDateTime lastCleanupTime;
        
        public SubscriptionStatistics(int totalSubscriptions, int totalUsers, int totalTemplates,
                                    long totalProcessed, long totalMatched, long totalNotified,
                                    long totalFailed, LocalDateTime lastCleanupTime) {
            this.totalSubscriptions = totalSubscriptions;
            this.totalUsers = totalUsers;
            this.totalTemplates = totalTemplates;
            this.totalProcessed = totalProcessed;
            this.totalMatched = totalMatched;
            this.totalNotified = totalNotified;
            this.totalFailed = totalFailed;
            this.lastCleanupTime = lastCleanupTime;
        }
        
        public double getMatchRate() {
            return totalProcessed > 0 ? (double) totalMatched / totalProcessed : 0.0;
        }
        
        public double getNotificationSuccessRate() {
            return totalMatched > 0 ? (double) totalNotified / totalMatched : 0.0;
        }
        
        // Getters
        public int getTotalSubscriptions() { return totalSubscriptions; }
        public int getTotalUsers() { return totalUsers; }
        public int getTotalTemplates() { return totalTemplates; }
        public long getTotalProcessed() { return totalProcessed; }
        public long getTotalMatched() { return totalMatched; }
        public long getTotalNotified() { return totalNotified; }
        public long getTotalFailed() { return totalFailed; }
        public LocalDateTime getLastCleanupTime() { return lastCleanupTime; }
    }
}
