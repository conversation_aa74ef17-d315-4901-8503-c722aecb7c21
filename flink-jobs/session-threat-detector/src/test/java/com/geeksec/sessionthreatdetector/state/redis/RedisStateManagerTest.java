package com.geeksec.sessionthreatdetector.state.redis;

import com.geeksec.sessionthreatdetector.killchain.model.AttackChainAnalysis;
import com.geeksec.sessionthreatdetector.killchain.model.CyberKillChainStage;
import com.geeksec.sessionthreatdetector.model.output.Alarm;
import com.geeksec.sessionthreatdetector.state.redis.DetectorStateManager.DetectorState;
import com.geeksec.sessionthreatdetector.detection.DetectorType;
import com.geeksec.sessionthreatdetector.state.redis.SessionStateManager.SessionInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis状态管理器测试类
 * 
 * <AUTHOR>
 */
public class RedisStateManagerTest {
    
    private static final Logger log = LoggerFactory.getLogger(RedisStateManagerTest.class);
    
    private ThreatDetectorStateManager stateManager;
    private RedisStateManager redisStateManager;
    private AttackChainStateManager attackChainStateManager;
    private AlarmStateManager alarmStateManager;
    private DetectorStateManager detectorStateManager;
    private SessionStateManager sessionStateManager;
    
    @BeforeEach
    void setUp() {
        stateManager = ThreatDetectorStateManager.getInstance();
        redisStateManager = stateManager.getRedisStateManager();
        attackChainStateManager = stateManager.getAttackChainStateManager();
        alarmStateManager = stateManager.getAlarmStateManager();
        detectorStateManager = stateManager.getDetectorStateManager();
        sessionStateManager = stateManager.getSessionStateManager();
    }
    
    @Test
    void testRedisBasicOperations() {
        log.info("测试Redis基本操作");
        
        // 测试字符串操作
        String key = "test:string:key";
        String value = "test_value";
        
        String result = redisStateManager.setString(key, value, 60);
        assertEquals("OK", result);
        
        String retrievedValue = redisStateManager.getString(key);
        assertEquals(value, retrievedValue);
        
        assertTrue(redisStateManager.exists(key));
        
        Long deleted = redisStateManager.delete(key);
        assertEquals(1L, deleted);
        
        assertFalse(redisStateManager.exists(key));
        
        log.info("Redis基本操作测试通过");
    }
    
    @Test
    void testRedisObjectOperations() {
        log.info("测试Redis对象操作");
        
        // 创建测试对象
        Map<String, Object> testObject = new HashMap<>();
        testObject.put("name", "test");
        testObject.put("value", 123);
        testObject.put("timestamp", LocalDateTime.now().toString());
        
        String key = "test:object:key";
        
        // 保存对象
        String result = redisStateManager.setObject(key, testObject, 60);
        assertEquals("OK", result);
        
        // 获取对象
        @SuppressWarnings("unchecked")
        Map<String, Object> retrievedObject = redisStateManager.getObject(key, Map.class);
        assertNotNull(retrievedObject);
        assertEquals("test", retrievedObject.get("name"));
        assertEquals(123, retrievedObject.get("value"));
        
        // 清理
        redisStateManager.delete(key);
        
        log.info("Redis对象操作测试通过");
    }
    
    @Test
    void testAttackChainStateManagement() {
        log.info("测试攻击链状态管理");
        
        // 创建测试攻击链分析
        AttackChainAnalysis analysis = AttackChainAnalysis.builder()
                .attackChainId("test-chain-001")
                .campaignName("测试攻击活动")
                .confidence(0.85)
                .analysisTime(LocalDateTime.now())
                .build();
        
        // 创建进展评估
        AttackChainAnalysis.AttackProgressAssessment progress = 
                AttackChainAnalysis.AttackProgressAssessment.builder()
                        .currentStage(CyberKillChainStage.COMMAND_AND_CONTROL)
                        .progressPercentage(75.0)
                        .successProbability(0.8)
                        .build();
        analysis.setProgressAssessment(progress);
        
        // 保存攻击链
        attackChainStateManager.saveAttackChainAnalysis(analysis);
        
        // 获取攻击链
        AttackChainAnalysis retrievedAnalysis = attackChainStateManager.getAttackChainAnalysis("test-chain-001");
        assertNotNull(retrievedAnalysis);
        assertEquals("test-chain-001", retrievedAnalysis.getAttackChainId());
        assertEquals("测试攻击活动", retrievedAnalysis.getCampaignName());
        assertEquals(0.85, retrievedAnalysis.getConfidence());
        
        // 测试统计信息
        AttackChainStateManager.AttackChainStatistics stats = attackChainStateManager.getStatistics();
        assertTrue(stats.getTotalChains() > 0);
        
        // 清理
        attackChainStateManager.deleteAttackChain("test-chain-001");
        
        log.info("攻击链状态管理测试通过");
    }
    
    @Test
    void testAlarmStateManagement() {
        log.info("测试告警状态管理");
        
        // 创建测试告警
        Alarm alarm = Alarm.builder()
                .alarmId("test-alarm-001")
                .alarmName("测试告警")
                .threatType("网络扫描")
                .srcIp("********")
                .dstIp("*************")
                .detectorName("TestDetector")
                .timestamp(LocalDateTime.now())
                .confidence(0.9)
                .build();
        
        // 保存告警
        alarmStateManager.saveAlarm(alarm);
        
        // 获取告警
        Alarm retrievedAlarm = alarmStateManager.getAlarm("test-alarm-001");
        assertNotNull(retrievedAlarm);
        assertEquals("test-alarm-001", retrievedAlarm.getAlarmId());
        assertEquals("测试告警", retrievedAlarm.getAlarmName());
        assertEquals("网络扫描", retrievedAlarm.getThreatType());
        
        // 测试去重
        boolean isDuplicate1 = alarmStateManager.isDuplicateAlarm(alarm);
        assertFalse(isDuplicate1); // 第一次不重复
        
        boolean isDuplicate2 = alarmStateManager.isDuplicateAlarm(alarm);
        assertTrue(isDuplicate2); // 第二次重复
        
        // 测试查询
        List<String> alarmsByThreatType = alarmStateManager.getAlarmsByThreatType("网络扫描", 10);
        assertTrue(alarmsByThreatType.contains("test-alarm-001"));
        
        List<String> alarmsBySrcIp = alarmStateManager.getAlarmsBySrcIp("********", 10);
        assertTrue(alarmsBySrcIp.contains("test-alarm-001"));
        
        // 测试统计
        AlarmStateManager.AlarmStatistics alarmStats = alarmStateManager.getAlarmStatistics(24);
        assertTrue(alarmStats.getTotalCount() > 0);
        assertTrue(alarmStats.getThreatTypeStats().containsKey("网络扫描"));
        
        // 清理
        alarmStateManager.deleteAlarm("test-alarm-001");
        
        log.info("告警状态管理测试通过");
    }
    
    @Test
    void testDetectorStateManagement() {
        log.info("测试检测器状态管理");
        
        // 创建检测器状态
        DetectorState detectorState = DetectorState.builder()
                .detectorName(DetectorType.WEBSHELL.name())
                .status(DetectorState.DetectorStatus.RUNNING)
                .statusDescription("运行正常")
                .startTime(LocalDateTime.now().minusHours(1))
                .build();
        
        String detectorKey = DetectorType.WEBSHELL.name();
        
        // 更新检测器状态
        detectorStateManager.updateDetectorState(detectorKey, detectorState);
        
        // 获取检测器状态
        DetectorState retrievedState = detectorStateManager.getDetectorState(detectorKey);
        assertNotNull(retrievedState);
        assertEquals(DetectorType.WEBSHELL.name(), retrievedState.getDetectorName());
        assertEquals(DetectorState.DetectorStatus.RUNNING, retrievedState.getStatus());
        
        // 测试检测器开关
        detectorStateManager.setDetectorEnabled(detectorKey, false);
        assertFalse(detectorStateManager.isDetectorEnabled(detectorKey));
        
        detectorStateManager.setDetectorEnabled(detectorKey, true);
        assertTrue(detectorStateManager.isDetectorEnabled(detectorKey));
        
        // 测试活跃检测器列表
        List<String> activeDetectors = detectorStateManager.getActiveDetectors();
        assertTrue(activeDetectors.contains(detectorKey));
        
        // 测试检测器概览
        DetectorStateManager.DetectorOverview overview = detectorStateManager.getDetectorOverview();
        assertTrue(overview.getTotalDetectors() > 0);
        assertTrue(overview.getRunningDetectors() > 0);
        assertTrue(overview.getDetectorStates().containsKey(detectorKey));
        
        log.info("检测器状态管理测试通过");
    }
    
    @Test
    void testSessionStateManagement() {
        log.info("测试会话状态管理");
        
        // 创建会话信息
        SessionInfo sessionInfo = SessionInfo.builder()
                .sessionId("test-session-001")
                .srcIp("********")
                .dstIp("*************")
                .srcPort(12345)
                .dstPort(80)
                .protocol("TCP")
                .startTime(LocalDateTime.now())
                .active(true)
                .anomalous(false)
                .build();
        
        // 更新会话状态
        sessionStateManager.updateSessionState("test-session-001", sessionInfo);
        
        // 获取会话状态
        SessionInfo retrievedSession = sessionStateManager.getSessionState("test-session-001");
        assertNotNull(retrievedSession);
        assertEquals("test-session-001", retrievedSession.getSessionId());
        assertEquals("********", retrievedSession.getSrcIp());
        assertEquals("TCP", retrievedSession.getProtocol());
        assertTrue(retrievedSession.isActive());
        
        // 测试会话查询
        List<String> sessionsBySrcIp = sessionStateManager.getSessionsBySrcIp("********", 10);
        assertTrue(sessionsBySrcIp.contains("test-session-001"));
        
        List<String> sessionsByProtocol = sessionStateManager.getSessionsByProtocol("TCP", 10);
        assertTrue(sessionsByProtocol.contains("test-session-001"));
        
        // 测试标记异常
        sessionStateManager.markSessionAsAnomalous("test-session-001", "异常流量", "检测到异常的网络流量模式");
        
        SessionInfo anomalousSession = sessionStateManager.getSessionState("test-session-001");
        assertTrue(anomalousSession.isAnomalous());
        assertNotNull(anomalousSession.getAnomalies());
        assertFalse(anomalousSession.getAnomalies().isEmpty());
        
        List<String> anomalousSessions = sessionStateManager.getAnomalousSessions(10);
        assertTrue(anomalousSessions.contains("test-session-001"));
        
        // 测试结束会话
        sessionStateManager.endSession("test-session-001", "正常结束");
        
        SessionInfo endedSession = sessionStateManager.getSessionState("test-session-001");
        assertFalse(endedSession.isActive());
        assertNotNull(endedSession.getEndTime());
        assertEquals("正常结束", endedSession.getEndReason());
        
        // 测试会话统计
        SessionStateManager.SessionStatistics sessionStats = sessionStateManager.getSessionStatistics(24);
        assertNotNull(sessionStats);
        assertTrue(sessionStats.getProtocolDistribution().containsKey("TCP"));
        
        // 清理
        sessionStateManager.deleteSession("test-session-001");
        
        log.info("会话状态管理测试通过");
    }
    
    @Test
    void testSystemStateOverview() {
        log.info("测试系统状态概览");
        
        // 获取系统状态概览
        ThreatDetectorStateManager.SystemStateOverview overview = stateManager.getSystemStateOverview();
        
        assertNotNull(overview);
        assertNotNull(overview.getOverviewTime());
        assertNotNull(overview.getRedisStatistics());
        assertNotNull(overview.getAttackChainStatistics());
        assertNotNull(overview.getDetectorOverview());
        
        log.info("系统状态概览: Redis操作数={}, 攻击链数={}, 检测器数={}",
                overview.getRedisStatistics().getTotalOperations(),
                overview.getAttackChainStatistics().getTotalChains(),
                overview.getDetectorOverview().getTotalDetectors());
        
        log.info("系统状态概览测试通过");
    }
    
    @Test
    void testSystemHealthStatus() {
        log.info("测试系统健康状态");
        
        // 获取系统健康状态
        ThreatDetectorStateManager.SystemHealthStatus healthStatus = stateManager.getSystemHealthStatus();
        
        assertNotNull(healthStatus);
        assertNotNull(healthStatus.getCheckTime());
        
        log.info("系统健康状态: 整体健康={}, Redis健康={}, 检测器健康={}, 检测器健康度={}%",
                healthStatus.isSystemHealthy(),
                healthStatus.isRedisHealthy(),
                healthStatus.isDetectorHealthy(),
                healthStatus.getDetectorHealthRatio() * 100);
        
        log.info("系统健康状态测试通过");
    }
    
    @Test
    void testSystemCleanup() {
        log.info("测试系统清理功能");
        
        // 创建一些测试数据
        createTestDataForCleanup();
        
        // 执行清理（清理1小时前的数据）
        ThreatDetectorStateManager.CleanupStatistics cleanupStats = stateManager.performSystemCleanup(1);
        
        assertNotNull(cleanupStats);
        assertNotNull(cleanupStats.getCleanupTime());
        assertTrue(cleanupStats.getCleanupDurationMs() > 0);
        
        log.info("清理统计: 成功={}, 攻击链清理={}, 告警清理={}, 会话清理={}, 总清理={}, 耗时={}ms",
                cleanupStats.isSuccess(),
                cleanupStats.getAttackChainsCleaned(),
                cleanupStats.getAlarmsCleaned(),
                cleanupStats.getSessionsCleaned(),
                cleanupStats.getTotalCleaned(),
                cleanupStats.getCleanupDurationMs());
        
        log.info("系统清理功能测试通过");
    }
    
    @Test
    void testRedisStatistics() {
        log.info("测试Redis统计信息");
        
        // 执行一些操作
        redisStateManager.setString("test:stats:1", "value1");
        redisStateManager.setString("test:stats:2", "value2");
        redisStateManager.getString("test:stats:1");
        redisStateManager.delete("test:stats:1", "test:stats:2");
        
        // 获取统计信息
        RedisStateManager.RedisStatistics stats = redisStateManager.getStatistics();
        
        assertNotNull(stats);
        assertTrue(stats.getTotalOperations() > 0);
        assertTrue(stats.getOperationStats().size() > 0);
        
        log.info("Redis统计: 总操作数={}, 错误数={}, 错误率={}%, 活跃连接={}, 空闲连接={}",
                stats.getTotalOperations(),
                stats.getErrorCount(),
                stats.getErrorRate() * 100,
                stats.getActiveConnections(),
                stats.getIdleConnections());
        
        log.info("Redis统计信息测试通过");
    }
    
    /**
     * 创建测试数据用于清理测试
     */
    private void createTestDataForCleanup() {
        // 创建过期的告警数据
        Alarm oldAlarm = Alarm.builder()
                .alarmId("old-alarm-001")
                .alarmName("过期告警")
                .threatType("测试威胁")
                .timestamp(LocalDateTime.now().minusHours(2))
                .build();
        alarmStateManager.saveAlarm(oldAlarm);
        
        // 创建过期的会话数据
        SessionInfo oldSession = SessionInfo.builder()
                .sessionId("old-session-001")
                .srcIp("********")
                .dstIp("*************")
                .protocol("TCP")
                .startTime(LocalDateTime.now().minusHours(2))
                .active(false)
                .build();
        sessionStateManager.updateSessionState("old-session-001", oldSession);
        
        log.debug("已创建测试数据用于清理测试");
    }
}
