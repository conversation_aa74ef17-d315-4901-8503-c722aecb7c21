package com.geeksec.sessionthreatdetector.subscription;

import com.geeksec.sessionthreatdetector.model.output.Alarm;
import com.geeksec.sessionthreatdetector.subscription.config.SubscriptionConfig;
import com.geeksec.sessionthreatdetector.subscription.manager.SubscriptionManager;
import com.geeksec.sessionthreatdetector.subscription.model.AlarmSubscription;
import com.geeksec.sessionthreatdetector.subscription.model.NotificationChannel;
import com.geeksec.sessionthreatdetector.subscription.model.NotificationTemplate;
import com.geeksec.sessionthreatdetector.subscription.model.SubscriptionRule;
import com.geeksec.sessionthreatdetector.subscription.notification.NotificationSender;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 订阅功能测试类
 * 
 * <AUTHOR>
 */
public class SubscriptionTest {
    
    private static final Logger log = LoggerFactory.getLogger(SubscriptionTest.class);
    
    private SubscriptionManager subscriptionManager;
    private NotificationSender notificationSender;
    
    @BeforeEach
    void setUp() {
        notificationSender = new NotificationSender();
        subscriptionManager = new SubscriptionManager(notificationSender);
    }
    
    @Test
    void testSubscriptionRule() {
        log.info("测试订阅规则");
        
        // 创建测试告警
        Alarm alarm = createTestAlarm("高危威胁", "恶意软件", "*************", "*************");
        
        // 测试字段匹配规则
        SubscriptionRule rule1 = SubscriptionRule.createFieldRule(
                "alarmType", SubscriptionRule.Operator.CONTAINS, "高危");
        assertTrue(rule1.matches(alarm));
        
        SubscriptionRule rule2 = SubscriptionRule.createFieldRule(
                "threatType", SubscriptionRule.Operator.EQUALS, "恶意软件");
        assertTrue(rule2.matches(alarm));
        
        SubscriptionRule rule3 = SubscriptionRule.createFieldRule(
                "srcIp", SubscriptionRule.Operator.STARTS_WITH, "192.168");
        assertTrue(rule3.matches(alarm));
        
        // 测试不匹配的规则
        SubscriptionRule rule4 = SubscriptionRule.createFieldRule(
                "alarmType", SubscriptionRule.Operator.CONTAINS, "低危");
        assertFalse(rule4.matches(alarm));
        
        log.info("订阅规则测试通过");
    }
    
    @Test
    void testNotificationTemplate() {
        log.info("测试通知模板");
        
        // 创建测试模板
        NotificationTemplate template = NotificationTemplate.createDefaultEmailTemplate();
        
        // 准备模板上下文
        Alarm alarm = createTestAlarm("高危威胁", "恶意软件", "*************", "*************");
        Map<String, Object> context = new HashMap<>();
        context.put("alarm", alarm);
        context.put("currentTime", LocalDateTime.now());
        
        // 渲染模板
        NotificationTemplate.RenderedTemplate rendered = template.render(context);
        
        assertNotNull(rendered);
        assertNotNull(rendered.getTitle());
        assertNotNull(rendered.getContent());
        assertTrue(rendered.getTitle().contains("高危威胁"));
        assertTrue(rendered.getContent().contains("*************"));
        
        log.info("渲染后的标题: {}", rendered.getTitle());
        log.info("渲染后的内容: {}", rendered.getContent());
        log.info("通知模板测试通过");
    }
    
    @Test
    void testNotificationChannel() {
        log.info("测试通知渠道");
        
        // 测试邮件渠道
        NotificationChannel emailChannel = NotificationChannel.createEmailChannel(
                "<EMAIL>", "default_email");
        assertTrue(emailChannel.isValid());
        assertTrue(emailChannel.isAvailable());
        
        // 测试短信渠道
        NotificationChannel smsChannel = NotificationChannel.createSmsChannel(
                "13800138000", "default_sms");
        assertTrue(smsChannel.isValid());
        assertTrue(smsChannel.isAvailable());
        
        // 测试Webhook渠道
        NotificationChannel webhookChannel = NotificationChannel.createWebhookChannel(
                "https://example.com/webhook", "default_webhook");
        assertTrue(webhookChannel.isValid());
        assertTrue(webhookChannel.isAvailable());
        
        // 测试无效渠道
        NotificationChannel invalidChannel = NotificationChannel.builder()
                .channelType(NotificationChannel.ChannelType.EMAIL)
                .address("invalid-email")
                .enabled(true)
                .build();
        assertFalse(invalidChannel.isValid());
        
        log.info("通知渠道测试通过");
    }
    
    @Test
    void testAlarmSubscription() {
        log.info("测试告警订阅");
        
        // 创建订阅
        AlarmSubscription subscription = AlarmSubscription.builder()
                .subscriptionId("test-subscription")
                .userId("test-user")
                .username("测试用户")
                .subscriptionName("测试订阅")
                .enabled(true)
                .priority(AlarmSubscription.SubscriptionPriority.HIGH)
                .createTime(LocalDateTime.now())
                .build();
        
        // 添加规则
        SubscriptionRule rule = SubscriptionRule.createFieldRule(
                "alarmType", SubscriptionRule.Operator.CONTAINS, "高危");
        subscription.addRule(rule);
        
        // 添加通知渠道
        NotificationChannel channel = NotificationChannel.createEmailChannel(
                "<EMAIL>", "default_email");
        subscription.addChannel(channel);
        
        // 验证订阅
        assertTrue(subscription.isValid());
        
        // 测试匹配
        Alarm matchingAlarm = createTestAlarm("高危威胁", "恶意软件", "*************", "*************");
        assertTrue(subscription.matches(matchingAlarm));
        
        Alarm nonMatchingAlarm = createTestAlarm("低危威胁", "可疑行为", "*************", "*************");
        assertFalse(subscription.matches(nonMatchingAlarm));
        
        log.info("告警订阅测试通过");
    }
    
    @Test
    void testSubscriptionManager() {
        log.info("测试订阅管理器");
        
        // 创建订阅
        AlarmSubscription subscription = createTestSubscription();
        subscriptionManager.addSubscription(subscription);
        
        // 测试处理告警
        Alarm alarm = createTestAlarm("高危威胁", "恶意软件", "*************", "*************");
        SubscriptionManager.SubscriptionProcessResult result = subscriptionManager.processAlarm(alarm);
        
        assertTrue(result.isSuccess());
        assertEquals(1, result.getMatchedCount());
        assertFalse(result.getNotificationResults().isEmpty());
        
        // 测试不匹配的告警
        Alarm nonMatchingAlarm = createTestAlarm("低危威胁", "可疑行为", "*************", "*************");
        SubscriptionManager.SubscriptionProcessResult nonMatchResult = 
                subscriptionManager.processAlarm(nonMatchingAlarm);
        
        assertTrue(nonMatchResult.isSuccess());
        assertEquals(0, nonMatchResult.getMatchedCount());
        
        // 检查统计信息
        SubscriptionManager.SubscriptionStatistics stats = subscriptionManager.getStatistics();
        assertEquals(1, stats.getTotalSubscriptions());
        assertEquals(1, stats.getTotalUsers());
        assertTrue(stats.getTotalProcessed() >= 2);
        
        log.info("订阅管理器测试通过");
    }
    
    @Test
    void testNotificationSender() {
        log.info("测试通知发送器");
        
        // 创建通知渠道
        NotificationChannel emailChannel = NotificationChannel.createEmailChannel(
                "<EMAIL>", "default_email");
        
        // 创建模板
        NotificationTemplate template = NotificationTemplate.createDefaultEmailTemplate();
        
        // 准备上下文
        Alarm alarm = createTestAlarm("高危威胁", "恶意软件", "*************", "*************");
        Map<String, Object> context = new HashMap<>();
        context.put("alarm", alarm);
        
        // 渲染模板
        NotificationTemplate.RenderedTemplate rendered = template.render(context);
        
        // 发送通知
        boolean sent = notificationSender.sendNotification(emailChannel, rendered);
        
        // 由于是模拟发送，结果可能成功也可能失败
        log.info("通知发送结果: {}", sent);
        
        // 检查支持的渠道类型
        assertTrue(notificationSender.isChannelTypeSupported(NotificationChannel.ChannelType.EMAIL));
        assertTrue(notificationSender.isChannelTypeSupported(NotificationChannel.ChannelType.SMS));
        assertTrue(notificationSender.isChannelTypeSupported(NotificationChannel.ChannelType.DINGTALK));
        
        log.info("通知发送器测试通过");
    }
    
    @Test
    void testSubscriptionConfig() {
        log.info("测试订阅配置");
        
        // 测试默认配置
        SubscriptionConfig defaultConfig = SubscriptionConfig.createDefault();
        assertTrue(defaultConfig.validate());
        assertTrue(defaultConfig.isEnabled());
        
        // 测试高性能配置
        SubscriptionConfig highPerfConfig = SubscriptionConfig.createHighPerformance();
        assertTrue(highPerfConfig.validate());
        assertEquals(4, highPerfConfig.getParallelism());
        assertTrue(highPerfConfig.isAsyncNotificationEnabled());
        
        // 测试安全优先配置
        SubscriptionConfig securityConfig = SubscriptionConfig.createSecurityFocused();
        assertTrue(securityConfig.validate());
        assertTrue(securityConfig.isSubscriptionAuthEnabled());
        assertTrue(securityConfig.isNotificationContentFilterEnabled());
        
        log.info("订阅配置测试通过");
    }
    
    @Test
    void testFrequencyControl() {
        log.info("测试频率控制");
        
        // 创建带频率控制的订阅
        AlarmSubscription subscription = createTestSubscription();
        
        // 设置频率控制
        AlarmSubscription.NotificationFrequency frequency = 
                AlarmSubscription.NotificationFrequency.builder()
                        .type(AlarmSubscription.NotificationFrequency.FrequencyType.INTERVAL)
                        .intervalMinutes(1)
                        .maxNotificationsPerDay(10)
                        .build();
        subscription.setFrequency(frequency);
        
        // 第一次应该可以发送
        assertTrue(subscription.shouldSendNotification(false));
        
        // 更新触发信息
        subscription.updateTriggerInfo();
        
        // 立即再次检查，应该被频率限制
        // 注意：这里的实现比较简单，实际应该有更复杂的频率控制逻辑
        
        log.info("频率控制测试通过");
    }
    
    @Test
    void testQuietHours() {
        log.info("测试免打扰时间");
        
        AlarmSubscription subscription = createTestSubscription();
        
        // 设置免打扰时间（当前时间前后1小时）
        LocalDateTime now = LocalDateTime.now();
        AlarmSubscription.QuietHours quietHours = AlarmSubscription.QuietHours.builder()
                .enabled(true)
                .startTime(now.minusHours(1).toLocalTime())
                .endTime(now.plusHours(1).toLocalTime())
                .urgentIgnoreQuiet(true)
                .build();
        subscription.setQuietHours(quietHours);
        
        // 普通告警应该被免打扰阻止
        assertFalse(subscription.shouldSendNotification(false));
        
        // 紧急告警应该忽略免打扰
        assertTrue(subscription.shouldSendNotification(true));
        
        log.info("免打扰时间测试通过");
    }
    
    /**
     * 创建测试告警
     */
    private Alarm createTestAlarm(String alarmType, String threatType, String srcIp, String dstIp) {
        return Alarm.builder()
                .alarmId("test-alarm-" + System.nanoTime())
                .alarmName("测试告警")
                .alarmType(alarmType)
                .threatType(threatType)
                .srcIp(srcIp)
                .dstIp(dstIp)
                .srcPort(12345)
                .dstPort(80)
                .protocol("TCP")
                .description("这是一个测试告警")
                .detectorName("TestDetector")
                .confidence(0.9)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建测试订阅
     */
    private AlarmSubscription createTestSubscription() {
        AlarmSubscription subscription = AlarmSubscription.builder()
                .subscriptionId("test-subscription-" + System.nanoTime())
                .userId("test-user")
                .username("测试用户")
                .subscriptionName("测试订阅")
                .description("用于测试的订阅")
                .enabled(true)
                .priority(AlarmSubscription.SubscriptionPriority.HIGH)
                .createTime(LocalDateTime.now())
                .build();
        
        // 添加规则：匹配高危告警
        SubscriptionRule rule = SubscriptionRule.createFieldRule(
                "alarmType", SubscriptionRule.Operator.CONTAINS, "高危");
        subscription.addRule(rule);
        
        // 添加邮件通知渠道
        NotificationChannel emailChannel = NotificationChannel.createEmailChannel(
                "<EMAIL>", "default_email");
        subscription.addChannel(emailChannel);
        
        return subscription;
    }
}
