package com.geeksec.sessionthreatdetector.killchain;

import com.geeksec.sessionthreatdetector.killchain.analyzer.AttackChainAnalyzer;
import com.geeksec.sessionthreatdetector.killchain.knowledge.AttackChainKnowledgeBase;
import com.geeksec.sessionthreatdetector.killchain.model.AttackChainAnalysis;
import com.geeksec.sessionthreatdetector.killchain.model.AttackChainEvent;
import com.geeksec.sessionthreatdetector.killchain.model.CyberKillChainStage;
import com.geeksec.sessionthreatdetector.model.output.Alarm;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 攻击链分析测试类
 * 
 * <AUTHOR>
 */
public class AttackChainAnalysisTest {
    
    private static final Logger log = LoggerFactory.getLogger(AttackChainAnalysisTest.class);
    
    private AttackChainAnalyzer attackChainAnalyzer;
    private AttackChainKnowledgeBase knowledgeBase;
    
    @BeforeEach
    void setUp() {
        attackChainAnalyzer = new AttackChainAnalyzer(60); // 60分钟关联窗口
        knowledgeBase = new AttackChainKnowledgeBase();
    }
    
    @Test
    void testCyberKillChainStages() {
        log.info("测试Cyber Kill Chain阶段定义");
        
        // 测试所有阶段
        CyberKillChainStage[] stages = CyberKillChainStage.values();
        assertEquals(7, stages.length);
        
        // 测试阶段顺序
        assertEquals(1, CyberKillChainStage.RECONNAISSANCE.getStageNumber());
        assertEquals(2, CyberKillChainStage.WEAPONIZATION.getStageNumber());
        assertEquals(3, CyberKillChainStage.DELIVERY.getStageNumber());
        assertEquals(4, CyberKillChainStage.EXPLOITATION.getStageNumber());
        assertEquals(5, CyberKillChainStage.INSTALLATION.getStageNumber());
        assertEquals(6, CyberKillChainStage.COMMAND_AND_CONTROL.getStageNumber());
        assertEquals(7, CyberKillChainStage.ACTIONS_ON_OBJECTIVES.getStageNumber());
        
        // 测试阶段分类
        assertTrue(CyberKillChainStage.RECONNAISSANCE.isEarlyStage());
        assertTrue(CyberKillChainStage.EXPLOITATION.isMidStage());
        assertTrue(CyberKillChainStage.COMMAND_AND_CONTROL.isLateStage());
        
        log.info("Cyber Kill Chain阶段测试通过");
    }
    
    @Test
    void testKnowledgeBase() {
        log.info("测试攻击链知识库");
        
        // 测试威胁类型映射
        List<CyberKillChainStage> scanStages = knowledgeBase.getStagesByThreatType("网络扫描");
        assertNotNull(scanStages);
        assertFalse(scanStages.isEmpty());
        assertEquals(CyberKillChainStage.RECONNAISSANCE, scanStages.get(0));
        
        List<CyberKillChainStage> malwareStages = knowledgeBase.getStagesByThreatType("恶意软件");
        assertNotNull(malwareStages);
        assertTrue(malwareStages.size() > 1); // 恶意软件可能在多个阶段
        
        // 测试攻击技术映射
        CyberKillChainStage portScanStage = knowledgeBase.getStageByTechnique("端口扫描");
        assertEquals(CyberKillChainStage.RECONNAISSANCE, portScanStage);
        
        CyberKillChainStage c2Stage = knowledgeBase.getStageByTechnique("C2通信");
        assertEquals(CyberKillChainStage.COMMAND_AND_CONTROL, c2Stage);
        
        // 测试MITRE ATT&CK战术映射
        CyberKillChainStage reconStage = knowledgeBase.getStageByTactic("Reconnaissance");
        assertEquals(CyberKillChainStage.RECONNAISSANCE, reconStage);
        
        CyberKillChainStage persistenceStage = knowledgeBase.getStageByTactic("Persistence");
        assertEquals(CyberKillChainStage.INSTALLATION, persistenceStage);
        
        log.info("攻击链知识库测试通过");
    }
    
    @Test
    void testSingleAlarmAnalysis() {
        log.info("测试单个告警的攻击链分析");
        
        // 创建网络扫描告警
        Alarm scanAlarm = createTestAlarm("网络扫描", "端口扫描活动", "10.0.0.1", "*************");
        
        // 执行分析
        AttackChainAnalyzer.AttackChainAnalysisResult result = attackChainAnalyzer.analyzeAlarm(scanAlarm);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getAttackChainId());
        assertEquals(CyberKillChainStage.RECONNAISSANCE, result.getKillChainStage());
        assertTrue(result.isNewChainCreated());
        
        AttackChainAnalysis analysis = result.getAttackChainAnalysis();
        assertNotNull(analysis);
        assertNotNull(analysis.getEvents());
        assertEquals(1, analysis.getEvents().size());
        
        log.info("单个告警分析测试通过: 攻击链={}, 阶段={}", 
                result.getAttackChainId(), result.getKillChainStage());
    }
    
    @Test
    void testMultiStageAttackChain() {
        log.info("测试多阶段攻击链分析");
        
        String attackerIp = "*************";
        String victimIp = "************";
        
        // 模拟完整的攻击链
        // 1. 侦察阶段
        Alarm reconAlarm = createTestAlarm("网络扫描", "网络侦察", attackerIp, victimIp);
        AttackChainAnalyzer.AttackChainAnalysisResult reconResult = attackChainAnalyzer.analyzeAlarm(reconAlarm);
        assertTrue(reconResult.isSuccess());
        assertEquals(CyberKillChainStage.RECONNAISSANCE, reconResult.getKillChainStage());
        
        // 2. 投递阶段（稍后时间）
        Alarm deliveryAlarm = createTestAlarmWithTime("钓鱼攻击", "恶意邮件投递", attackerIp, victimIp, 
                LocalDateTime.now().plusMinutes(10));
        AttackChainAnalyzer.AttackChainAnalysisResult deliveryResult = attackChainAnalyzer.analyzeAlarm(deliveryAlarm);
        assertTrue(deliveryResult.isSuccess());
        assertEquals(CyberKillChainStage.DELIVERY, deliveryResult.getKillChainStage());
        
        // 应该关联到同一个攻击链
        assertEquals(reconResult.getAttackChainId(), deliveryResult.getAttackChainId());
        
        // 3. 利用阶段
        Alarm exploitAlarm = createTestAlarmWithTime("漏洞利用", "缓冲区溢出攻击", attackerIp, victimIp,
                LocalDateTime.now().plusMinutes(20));
        AttackChainAnalyzer.AttackChainAnalysisResult exploitResult = attackChainAnalyzer.analyzeAlarm(exploitAlarm);
        assertTrue(exploitResult.isSuccess());
        assertEquals(CyberKillChainStage.EXPLOITATION, exploitResult.getKillChainStage());
        assertEquals(reconResult.getAttackChainId(), exploitResult.getAttackChainId());
        
        // 4. 安装阶段
        Alarm installAlarm = createTestAlarmWithTime("Webshell", "后门安装", attackerIp, victimIp,
                LocalDateTime.now().plusMinutes(30));
        AttackChainAnalyzer.AttackChainAnalysisResult installResult = attackChainAnalyzer.analyzeAlarm(installAlarm);
        assertTrue(installResult.isSuccess());
        assertEquals(CyberKillChainStage.INSTALLATION, installResult.getKillChainStage());
        assertEquals(reconResult.getAttackChainId(), installResult.getAttackChainId());
        
        // 5. 命令控制阶段
        Alarm c2Alarm = createTestAlarmWithTime("DNS隧道", "C2通信建立", attackerIp, victimIp,
                LocalDateTime.now().plusMinutes(40));
        AttackChainAnalyzer.AttackChainAnalysisResult c2Result = attackChainAnalyzer.analyzeAlarm(c2Alarm);
        assertTrue(c2Result.isSuccess());
        assertEquals(CyberKillChainStage.COMMAND_AND_CONTROL, c2Result.getKillChainStage());
        assertEquals(reconResult.getAttackChainId(), c2Result.getAttackChainId());
        
        // 6. 目标行动阶段
        Alarm actionAlarm = createTestAlarmWithTime("数据窃取", "敏感数据泄露", attackerIp, victimIp,
                LocalDateTime.now().plusMinutes(50));
        AttackChainAnalyzer.AttackChainAnalysisResult actionResult = attackChainAnalyzer.analyzeAlarm(actionAlarm);
        assertTrue(actionResult.isSuccess());
        assertEquals(CyberKillChainStage.ACTIONS_ON_OBJECTIVES, actionResult.getKillChainStage());
        assertEquals(reconResult.getAttackChainId(), actionResult.getAttackChainId());
        
        // 验证最终攻击链
        AttackChainAnalysis finalAnalysis = actionResult.getAttackChainAnalysis();
        assertNotNull(finalAnalysis);
        assertEquals(6, finalAnalysis.getEvents().size());
        
        // 验证进展评估
        assertNotNull(finalAnalysis.getProgressAssessment());
        assertEquals(CyberKillChainStage.ACTIONS_ON_OBJECTIVES, 
                finalAnalysis.getProgressAssessment().getCurrentStage());
        assertTrue(finalAnalysis.getProgressAssessment().getProgressPercentage() > 80);
        
        // 验证威胁评估
        assertNotNull(finalAnalysis.getThreatAssessment());
        assertTrue(finalAnalysis.getThreatAssessment().getRiskScore() > 70);
        
        log.info("多阶段攻击链测试通过: 攻击链={}, 事件数={}, 进展={}%", 
                finalAnalysis.getAttackChainId(), finalAnalysis.getEvents().size(),
                finalAnalysis.getProgressAssessment().getProgressPercentage());
    }
    
    @Test
    void testAttackChainCorrelation() {
        log.info("测试攻击链关联");
        
        String attackerIp = "*************";
        String victim1 = "************";
        String victim2 = "************";
        
        // 同一攻击者对不同目标的攻击应该关联到同一攻击链
        Alarm alarm1 = createTestAlarm("网络扫描", "扫描活动1", attackerIp, victim1);
        AttackChainAnalyzer.AttackChainAnalysisResult result1 = attackChainAnalyzer.analyzeAlarm(alarm1);
        
        Alarm alarm2 = createTestAlarmWithTime("网络扫描", "扫描活动2", attackerIp, victim2,
                LocalDateTime.now().plusMinutes(5));
        AttackChainAnalyzer.AttackChainAnalysisResult result2 = attackChainAnalyzer.analyzeAlarm(alarm2);
        
        // 应该关联到同一攻击链
        assertEquals(result1.getAttackChainId(), result2.getAttackChainId());
        
        // 不同攻击者的攻击应该创建不同的攻击链
        Alarm alarm3 = createTestAlarm("网络扫描", "扫描活动3", "*************", victim1);
        AttackChainAnalyzer.AttackChainAnalysisResult result3 = attackChainAnalyzer.analyzeAlarm(alarm3);
        
        assertNotEquals(result1.getAttackChainId(), result3.getAttackChainId());
        
        log.info("攻击链关联测试通过");
    }
    
    @Test
    void testAttackProgressEvaluation() {
        log.info("测试攻击进展评估");
        
        String attackerIp = "**************";
        String victimIp = "*************";
        
        // 创建不同阶段的告警
        Alarm[] alarms = {
                createTestAlarm("网络扫描", "侦察", attackerIp, victimIp),
                createTestAlarmWithTime("漏洞利用", "利用", attackerIp, victimIp, LocalDateTime.now().plusMinutes(10)),
                createTestAlarmWithTime("Webshell", "安装", attackerIp, victimIp, LocalDateTime.now().plusMinutes(20)),
                createTestAlarmWithTime("DNS隧道", "C2", attackerIp, victimIp, LocalDateTime.now().plusMinutes(30))
        };
        
        AttackChainAnalyzer.AttackChainAnalysisResult lastResult = null;
        for (Alarm alarm : alarms) {
            lastResult = attackChainAnalyzer.analyzeAlarm(alarm);
            assertTrue(lastResult.isSuccess());
        }
        
        // 验证进展评估
        AttackChainAnalysis analysis = lastResult.getAttackChainAnalysis();
        assertNotNull(analysis.getProgressAssessment());
        
        AttackChainAnalysis.AttackProgressAssessment progress = analysis.getProgressAssessment();
        assertEquals(CyberKillChainStage.COMMAND_AND_CONTROL, progress.getCurrentStage());
        assertTrue(progress.getProgressPercentage() > 50);
        assertTrue(progress.getSuccessProbability() > 0.5);
        assertNotNull(progress.getNextPossibleActions());
        assertFalse(progress.getNextPossibleActions().isEmpty());
        
        log.info("攻击进展评估测试通过: 当前阶段={}, 进展={}%, 成功概率={}%",
                progress.getCurrentStage(), progress.getProgressPercentage(),
                progress.getSuccessProbability() * 100);
    }
    
    @Test
    void testAttackChainTimeline() {
        log.info("测试攻击链时间线");
        
        String attackerIp = "*************";
        String victimIp = "*************";
        
        LocalDateTime baseTime = LocalDateTime.now();
        
        // 创建时间序列攻击
        Alarm[] timelineAlarms = {
                createTestAlarmWithTime("网络扫描", "扫描", attackerIp, victimIp, baseTime),
                createTestAlarmWithTime("漏洞利用", "利用", attackerIp, victimIp, baseTime.plusMinutes(15)),
                createTestAlarmWithTime("Webshell", "安装", attackerIp, victimIp, baseTime.plusMinutes(30)),
                createTestAlarmWithTime("数据窃取", "窃取", attackerIp, victimIp, baseTime.plusMinutes(45))
        };
        
        AttackChainAnalyzer.AttackChainAnalysisResult lastResult = null;
        for (Alarm alarm : timelineAlarms) {
            lastResult = attackChainAnalyzer.analyzeAlarm(alarm);
        }
        
        // 验证时间线
        AttackChainAnalysis analysis = lastResult.getAttackChainAnalysis();
        assertNotNull(analysis.getTimeline());
        
        AttackChainAnalysis.AttackChainTimeline timeline = analysis.getTimeline();
        assertEquals(baseTime, timeline.getStartTime());
        assertEquals(45 * 60, timeline.getDurationSeconds()); // 45分钟
        assertNotNull(timeline.getTimelineEvents());
        assertEquals(4, timeline.getTimelineEvents().size());
        
        log.info("攻击链时间线测试通过: 开始时间={}, 持续时间={}秒, 事件数={}",
                timeline.getStartTime(), timeline.getDurationSeconds(), timeline.getTimelineEvents().size());
    }
    
    @Test
    void testAttackPatternRecognition() {
        log.info("测试攻击模式识别");
        
        // 测试APT攻击模式
        String aptAttacker = "198.51.100.200";
        String aptVictim = "************0";
        
        // 创建完整的APT攻击链
        CyberKillChainStage[] aptStages = CyberKillChainStage.values();
        String[] aptThreatTypes = {"网络扫描", "恶意软件", "钓鱼攻击", "漏洞利用", "Webshell", "DNS隧道", "数据窃取"};
        
        AttackChainAnalyzer.AttackChainAnalysisResult aptResult = null;
        for (int i = 0; i < aptStages.length; i++) {
            Alarm alarm = createTestAlarmWithTime(aptThreatTypes[i], "APT攻击-" + aptStages[i].getChineseName(),
                    aptAttacker, aptVictim, LocalDateTime.now().plusMinutes(i * 10));
            aptResult = attackChainAnalyzer.analyzeAlarm(alarm);
        }
        
        // 验证APT模式特征
        AttackChainAnalysis aptAnalysis = aptResult.getAttackChainAnalysis();
        assertNotNull(aptAnalysis);
        assertEquals(7, aptAnalysis.getEvents().size());
        assertTrue(aptAnalysis.getProgressAssessment().getProgressPercentage() > 90);
        
        log.info("攻击模式识别测试通过");
    }
    
    @Test
    void testAnalyzerStatistics() {
        log.info("测试分析器统计信息");
        
        // 执行多次分析
        for (int i = 0; i < 10; i++) {
            Alarm alarm = createTestAlarm("网络扫描", "测试扫描" + i, "10.0.0." + i, "*************");
            attackChainAnalyzer.analyzeAlarm(alarm);
        }
        
        // 获取统计信息
        AttackChainAnalyzer.AnalysisStatistics stats = attackChainAnalyzer.getStatistics();
        
        assertTrue(stats.getTotalAnalyzed() >= 10);
        assertTrue(stats.getChainsCreated() > 0);
        assertTrue(stats.getActiveChains() > 0);
        assertTrue(stats.getChainCreationRate() > 0);
        
        log.info("分析器统计测试通过: 总分析={}, 创建链={}, 活跃链={}, 创建率={}%",
                stats.getTotalAnalyzed(), stats.getChainsCreated(), stats.getActiveChains(),
                stats.getChainCreationRate() * 100);
    }
    
    @Test
    void testAttackChainCleanup() {
        log.info("测试攻击链清理");
        
        // 创建一些攻击链
        for (int i = 0; i < 5; i++) {
            Alarm alarm = createTestAlarm("网络扫描", "清理测试" + i, "10.0.1." + i, "*************");
            attackChainAnalyzer.analyzeAlarm(alarm);
        }
        
        int beforeCleanup = attackChainAnalyzer.getStatistics().getActiveChains();
        assertTrue(beforeCleanup > 0);
        
        // 执行清理（清理0小时前的，即清理所有）
        attackChainAnalyzer.cleanupExpiredChains(0);
        
        int afterCleanup = attackChainAnalyzer.getStatistics().getActiveChains();
        assertEquals(0, afterCleanup);
        
        log.info("攻击链清理测试通过: 清理前={}, 清理后={}", beforeCleanup, afterCleanup);
    }
    
    /**
     * 创建测试告警
     */
    private Alarm createTestAlarm(String threatType, String alarmName, String srcIp, String dstIp) {
        return createTestAlarmWithTime(threatType, alarmName, srcIp, dstIp, LocalDateTime.now());
    }
    
    /**
     * 创建带时间的测试告警
     */
    private Alarm createTestAlarmWithTime(String threatType, String alarmName, String srcIp, String dstIp, 
                                        LocalDateTime timestamp) {
        return Alarm.builder()
                .alarmId("test-alarm-" + System.nanoTime())
                .alarmName(alarmName)
                .alarmType("高危威胁")
                .threatType(threatType)
                .srcIp(srcIp)
                .dstIp(dstIp)
                .srcPort(12345)
                .dstPort(80)
                .protocol("TCP")
                .description("这是一个测试告警：" + alarmName)
                .detectorName("TestDetector")
                .confidence(0.85)
                .timestamp(timestamp)
                .build();
    }
}
